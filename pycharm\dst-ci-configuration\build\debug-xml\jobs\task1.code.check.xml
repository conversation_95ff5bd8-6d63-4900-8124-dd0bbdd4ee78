<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Starts code check jobs.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_branch=task1
code_folder=tasks
non_virtual_code_branch=
non_virtual_code_folder=
project_name=bct</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.code

import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.code_branch)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * code_check.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def filter_paths = branchfile.general_settings?.filter_paths_code_check ?: []
                    P4PreviewCode(project, 'check', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, [], filter_paths, settings_map)
                }
            }
        }
        stage('Set code changelist') {
            steps {
                script {
                    def code_changelist = params.code_changelist ?: env.P4_CHANGELIST

                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>