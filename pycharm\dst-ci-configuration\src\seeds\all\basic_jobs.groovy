// codenarc-disable UnnecessaryObjectReferences
package all

import com.ea.exceptions.CobraException
import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.LibSlack
import com.ea.lib.jobs.LibBilbo
import com.ea.lib.jobs.LibCode
import com.ea.lib.jobs.LibCustomScript
import com.ea.lib.jobs.LibData
import com.ea.lib.jobs.LibFrosty
import com.ea.lib.jobs.LibFrostyOrchestratorScheduler
import com.ea.lib.jobs.LibPipelineDeterminismTest
import com.ea.lib.jobs.LibShift
import com.ea.lib.jobs.LibUnitTests
import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All
import javaposse.jobdsl.dsl.jobs.FreeStyleJob

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.branches

    branches.each { String current_branch, info ->
        out.println("   Processing branch: $current_branch")
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = info.project
        }
        def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
        def code_matrix = branchfile.code_matrix
        def code_nomaster_matrix = branchfile.code_nomaster_matrix
        def code_stressbulkbuild_matrix = branchfile.metaClass.hasProperty(branchfile, 'code_stressbulkbuild_matrix')
            ? branchfile.code_stressbulkbuild_matrix
            : []
        def data_matrix = branchfile.data_matrix
        def patchdata_matrix = branchfile.patchdata_matrix
        def frosty_matrix = branchfile.frosty_matrix
        def frosty_for_patch_matrix = branchfile.frosty_for_patch_matrix
        def patchfrosty_matrix = branchfile.patchfrosty_matrix
        def spin_upload_matrix = branchfile.spin_upload_matrix
        def frostbite_syncer_setup = branchfile?.hasProperty('frostbite_syncer_setup') ? branchfile.frostbite_syncer_setup : project.frostbite_syncer_setup
        def azure_uploads_matrix = branchfile.azure_uploads_matrix
        def general_settings = branchfile.general_settings
        def standard_jobs_settings = branchfile.standard_jobs_settings
        def freestyle_jobs = []
        def pipeline_determinism_test_matrix = branchfile.metaClass.hasProperty(branchfile, 'pipeline_determinism_test_matrix')
            ? branchfile.pipeline_determinism_test_matrix
            : []
        Map branch_info = info + general_settings + standard_jobs_settings + [branch_name: current_branch, project: project]
        String p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)
        String pipelineLogCodeBranch = branch_info.non_virtual_code_branch ?: branch_info.code_branch

        // Start and check jobs
        if (!code_matrix.isEmpty()) {
            out.println('       Processing code_matrix...')
            def codepreflight_info_sending = false
            if (branch_info.remote_masters_to_receive_code) {
                codepreflight_info_sending = true
            }
            def code_start = pipelineJob(current_branch + '.code.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/code/code_start.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCode.code_start(code_start, project, branch_info + [codepreflight_info_sending: codepreflight_info_sending])
        }

        if (!code_nomaster_matrix.isEmpty()) {
            out.println('       Processing code_nomaster_matrix...')
            def code_nomaster_start = pipelineJob(current_branch + '.code.nomaster.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/code/code_nomaster_start.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCode.code_start(code_nomaster_start, project, branch_info + [nomaster: true])
        }

        if (!code_stressbulkbuild_matrix.isEmpty()) {
            out.println('       Processing code_stressbulkbuild_matrix (start job)...')
            def code_stressbulkbuild_start = pipelineJob(current_branch + '.code.stressbulkbuild.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/code/code_stressbulkbuild_start.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCode.code_start(code_stressbulkbuild_start, project, branch_info + [stressbulkbuild: true])
        }

        if (frostbite_syncer_setup == true) {
            out.println('       Processing frostbite_syncer_setup...')
            def code_check = pipelineJob(current_branch + '.code.check') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/code/code_check.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCode.code_check(code_check, project, branch_info)
        }

        if (frostbite_syncer_setup == true) {
            out.println('       Processing frostbite_syncer_setup...')
            def copy_code_to_filer = job(current_branch + '.code.copy-to-filer') {}
            freestyle_jobs.add(copy_code_to_filer)
            LibCode.copy_code_to_filer(copy_code_to_filer, project, branch_info)
            LibJobDsl.kill_processes(copy_code_to_filer, branch_info)
            LibJobDsl.initialP4revert(copy_code_to_filer, project, branch_info)
            LibJobDsl.addVaultSecrets(copy_code_to_filer, branch_info)
            LibJobDsl.archive_non_build_logs(copy_code_to_filer, branch_info)
            LibJobDsl.postclean_silverback(copy_code_to_filer, project, branch_info)
        }

        if (!data_matrix.isEmpty()) {
            out.println('       Processing data_matrix...')
            def datapreflight_info_sending = false

            if (branch_info.remote_masters_to_receive_data) {
                datapreflight_info_sending = true
            }

            if (branch_info.verified_data_branch == true) {
                // Trigger jobs that use smoked binaries to verify data.
                def verified_data_start = pipelineJob(current_branch + '.verified-data.start') {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/data/verified_data_start.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibData.data_start(verified_data_start, project, branch_info + [verified_data: true, datapreflight_info_sending: datapreflight_info_sending])
            } else {
                if (branch_info.deployment_data_branch == true) {
                    // Trigger jobs that use smoked binaries to verify data.
                    def deployment_data_start = pipelineJob(current_branch + '.deployment-data.start') {
                        definition {
                            cps {
                                script(readFileFromWorkspace('src/scripts/schedulers/data/deployment_data_start.groovy'))
                                sandbox(true)
                            }
                        }
                    }
                    LibData.data_start(deployment_data_start, project, branch_info + [deployment_data: true, datapreflight_info_sending: datapreflight_info_sending])
                }
                def data_start = pipelineJob(current_branch + '.data.start') {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/data/data_start.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibData.data_start(data_start, project, branch_info + [datapreflight_info_sending: datapreflight_info_sending])
                if (branch_info.frosty_orchestrator_trigger) {
                    def frostyOrchestratorScheduler = pipelineJob("${current_branch}.frosty-orchestrator.start") {
                        definition {
                            cps {
                                script(readFileFromWorkspace('src/scripts/schedulers/data/FrostyOrchestratorScheduler.groovy'))
                                sandbox(true)
                            }
                        }
                    }
                    LibFrostyOrchestratorScheduler.start(frostyOrchestratorScheduler, project, branchfile, masterSettings, current_branch)
                }
            }

            if (branch_info.export_data_branch == true) {
                if ((branch_info.statebuild_data != false && branch_info.dry_run_data != true) || branch_info.export_data_branch == true) {
                    def export_data_start = pipelineJob(current_branch + '.export-data.start') {
                        definition {
                            cps {
                                script(readFileFromWorkspace('src/scripts/schedulers/all/export_data_scheduler.groovy'))
                                sandbox(true)
                            }
                        }
                    }
                    LibData.data_start(export_data_start, project, branch_info + [export_data: true])
                }
            }

            if (branch_info.enable_clean_build_validation == true) {
                def clean_data_validation_start = pipelineJob(current_branch + '.clean-data-validation.start') {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/all/clean_data_validation.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibData.data_start(clean_data_validation_start, project, branch_info + [clean_build_validation: true])
            }

            // Enlighten light bake start job
            generate_enlighten_start_job = false
            for (entry in data_matrix) {
                if (entry instanceof Map) {
                    if (entry.enlighten_bake_group != null) {
                        generate_enlighten_start_job = true
                        break
                    }
                }
            }
            if (generate_enlighten_start_job) {
                for (platform in data_matrix) {
                    for (group in platform.enlighten_bake_group) {
                        // Trigger jobs that use smoked binaries and verified data to build enlighten data.
                        def enlighten_start_args = [
                            data_reference_job        : branch_info.branch_name + '.data.start',
                            datapreflight_info_sending: false,
                            trigger_string_data       : group.cron_string,
                            trigger_type_data         : 'cron',
                        ]
                        def enlighten_start = pipelineJob(current_branch + '.' + group.group_name + '.enlighten.start') {
                            definition {
                                cps {
                                    script(readFileFromWorkspace('src/scripts/schedulers/data/enlighten_start.groovy'))
                                    sandbox(true)
                                }
                            }
                        }
                        LibData.data_start(enlighten_start, project, branch_info + enlighten_start_args)
                    }
                }
            }
        }

        if (!patchdata_matrix.isEmpty()) {
            out.println('       Processing patchdata_matrix...')
            def patchdata_start = pipelineJob(current_branch + '.patchdata.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/data/patchdata_start.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibData.patchdata_start(patchdata_start, project, branch_info)
        }

        if (!frosty_matrix.isEmpty() && branch_info.skip_frosty_scheduler != true) {
            out.println('       Processing frosty_matrix...')
            def frosty_start = pipelineJob(current_branch + '.frosty.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/frosty/frosty_start.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibFrosty.frosty_start(frosty_start, project, branch_info)
        }

        if (!patchfrosty_matrix.isEmpty() || !frosty_for_patch_matrix.isEmpty()) {
            out.println('       Processing patchfrosty_matrix...')
            def patchfrosty_start = pipelineJob(current_branch + '.patchfrosty.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/frosty/patchfrosty_start.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibFrosty.patchfrosty_start(patchfrosty_start, project, branch_info)
        }

        if (branch_info.shift_branch == true && branch_info.shift_every_build != true) {
            out.println('       Processing shift_branch...')
            if (!frosty_matrix.isEmpty() || !frosty_for_patch_matrix.isEmpty() || !patchfrosty_matrix.isEmpty()) {
                def shift_start = pipelineJob(current_branch + '.shift.start') {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/all/shift_scheduler.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibShift.shift_start(shift_start, project, branchfile, masterSettings, current_branch)
            }
        }

        if (branch_info.unittests) {
            out.println('       Processing unit tests...')
            def unitTestsStart = pipelineJob("${current_branch}.unittests.start") {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/UnitTestsScheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibUnitTests.start(unitTestsStart, project, branchfile, masterSettings, current_branch)

            def unitTestsJob = job("${current_branch}.unittests.job") {}
            freestyle_jobs.add(unitTestsJob)
            LibUnitTests.job(unitTestsJob, project, branchfile, masterSettings, current_branch)
            LibScm.sync_code(unitTestsJob, project, branch_info, '${CODE_CHANGELIST}')
            LibJobDsl.kill_processes(unitTestsJob, branch_info)
            LibJobDsl.initialP4revert(unitTestsJob, project, branch_info, true, false)
            LibJobDsl.addVaultSecrets(unitTestsJob, branch_info)
            LibJobDsl.archive_non_build_logs(unitTestsJob, branch_info)
            LibJobDsl.postclean_silverback(unitTestsJob, project, branch_info)
        }

        if (branch_info.verify_for_preflight) {
            def verifiedForPreflight = job(current_branch + '.data.start.register.verifiedForPreflight') {}
            freestyle_jobs.add(verifiedForPreflight)
            LibBilbo.bilbo_register_verified_for_preflight(verifiedForPreflight, project, branchfile, masterSettings, current_branch, 'data_changelist')
            LibJobDsl.addVaultSecrets(verifiedForPreflight, branch_info)
        }

        for (platform in code_matrix) {
            for (config in platform.configs) {
                out.println("       Processing code_matrix $platform.name $config...")
                def config_name = config
                def dry_run_config = false
                def havok_run = false
                def compile_unit_tests = false
                def run_unit_tests = false
                def mimalloc_enabled = branch_info?.mimalloc_enabled
                def fb_env_values_code = []
                def custom_tag = null
                def extra_args = ''
                def asan_enabled = false
                def ubsan_enabled = false
                def sndbs_enabled_config = null
                def tool_targets = []
                if (config instanceof Map) {
                    config_name = config.name
                    dry_run_config = config.dry_run ?: false
                    havok_run = config.havok_run ?: false
                    fb_env_values_code = config.fb_env_values ?: []
                    compile_unit_tests = config.compile_unit_tests ?: false
                    run_unit_tests = config.run_unit_tests ?: false
                    custom_tag = config?.custom_tag
                    extra_args = config?.extra_args
                    asan_enabled = config.asan_enabled ?: false
                    ubsan_enabled = config.ubsan_enabled ?: false
                    sndbs_enabled_config = config.sndbs_enabled != null ? config.sndbs_enabled : null
                    tool_targets = config.tool_targets ?: []
                }
                def code_job_info = [
                    config              : config_name,
                    dry_run_config      : dry_run_config,
                    havok_run           : havok_run,
                    mimalloc_enabled    : mimalloc_enabled,
                    platform            : platform.name,
                    fb_env_values_code  : fb_env_values_code,
                    compile_unit_tests  : compile_unit_tests,
                    run_unit_tests      : run_unit_tests,
                    custom_tag          : custom_tag,
                    extra_args          : extra_args,
                    asan_enabled        : asan_enabled,
                    ubsan_enabled       : ubsan_enabled,
                    sndbs_enabled_config: sndbs_enabled_config,
                    tool_targets        : tool_targets,
                ]

                def job_name = current_branch + '.code.' + platform.name + '.' + config_name
                if (custom_tag != null) {
                    job_name = job_name + ".${custom_tag}"
                }

                def code_job = job(job_name) {}
                freestyle_jobs.add(code_job)
                LibCode.code_job(code_job, project, branch_info + code_job_info)
                if (branch_info.code_data_sync == true || run_unit_tests) {
                    LibScm.sync_code_and_data(code_job, project, branch_info, '', '${code_changelist}')
                } else {
                    LibScm.sync_code(code_job, project, branch_info, '${code_changelist}')
                }
                LibJobDsl.kill_processes(code_job, branch_info)
                backup_local = branch_info.backup_local ?: false
                if (backup_local) {
                    LibJobDsl.backup_local(code_job, branch_info)
                }
                LibJobDsl.initialP4revert(code_job, project, branch_info, true, false)
                if (!project.is_cloud) {
                    LibJobDsl.archive_non_build_logs(code_job, branch_info)
                }
                LibJobDsl.addVaultSecrets(code_job, branch_info)
                LibJobDsl.postclean_silverback(code_job, project, branch_info)
                LibJobDsl.claim_builds(code_job, branch_info)

                def azure_uploads = azure_uploads_matrix.findAll { it.content_type.contains('code') && it.platform == platform.name && it.config.contains(config_name) }
                def additional_tools = branch_info.azure_fileshare?.additional_tools_to_include
                azure_uploads.each { upload ->
                    def azureJobName = "${current_branch}.code.${platform.name}.${config_name}.copy-build-to-azure"
                    def azure_branch_info = branch_info + [
                        'platform'                   : platform.name,
                        'config'                     : config_name,
                        'content_type'               : 'code',
                        'additional_tools_to_include': additional_tools ? additional_tools.join(', ') : null,
                        'target_build_share'         : branch_info.azure_fileshare?.target_build_share,
                        'secret_context'             : branch_info.azure_fileshare?.secret_context,
                        'node_label'                 : 'copy_build_to_azure',
                    ]
                    LibJobDsl.standardPipelineJob(
                        this,
                        azureJobName,
                        'src/scripts/schedulers/all/azure_upload_scheduler.groovy').with {
                        environmentVariables(azure_branch_info)
                        parameters {
                            stringParam {
                                name('code_changelist')
                                defaultValue('')
                                description('Specifies code changelist for upload.')
                                trim(true)
                            }
                        }
                        parameters {
                            stringParam {
                                name('source')
                                defaultValue('')
                                description('Skip path logic and use this path as source')
                                trim(true)
                            }
                        }
                        parameters {
                            stringParam {
                                name('destination')
                                defaultValue('')
                                description('Skip path logic and use this path as destination')
                                trim(true)
                            }
                        }
                        parameters {
                            stringParam {
                                name('target_build_share')
                                defaultValue(azure_branch_info.target_build_share)
                                description('elipy config key to find buildshare in alternate_build_shares.')
                                trim(true)
                            }
                        }
                        parameters {
                            stringParam {
                                name('secret_context')
                                defaultValue(azure_branch_info.secret_context)
                                description('Elipy config secrets where key for filer auth')
                                trim(true)
                            }
                        }
                        parameters {
                            stringParam {
                                name('additional_tools_to_include')
                                defaultValue(azure_branch_info.additional_tools_to_include)
                                description('Additional tool(s) to pull from network share')
                                trim(true)
                            }
                        }
                    }
                }
            }
        }

        for (platform in code_nomaster_matrix) {
            for (config in platform.configs) {
                out.println("       Processing code_nomaster_matrix $platform $config...")
                def config_name = config
                def dry_run_config = false
                if (config instanceof Map) {
                    config_name = config.name
                    dry_run_config = config.dry_run ?: false
                }
                def code_nomaster_job_info = [
                    config        : config_name,
                    dry_run_config: dry_run_config,
                    nomaster      : true,
                    platform      : platform.name,
                ]

                def code_nomaster_job = job(current_branch + '.code.nomaster.' + platform.name + '.' + config_name) {}
                freestyle_jobs.add(code_nomaster_job)
                LibCode.code_job(code_nomaster_job, project, branch_info + code_nomaster_job_info)
                if (branch_info.code_data_sync == true) {
                    LibScm.sync_code_and_data(code_nomaster_job, project, branch_info, '', '${code_changelist}')
                } else {
                    LibScm.sync_code(code_nomaster_job, project, branch_info, '${code_changelist}')
                }
                LibJobDsl.kill_processes(code_nomaster_job, branch_info)
                LibJobDsl.initialP4revert(code_nomaster_job, project, branch_info, true, false)
                LibJobDsl.addVaultSecrets(code_nomaster_job, branch_info)
                LibJobDsl.archive_non_build_logs(code_nomaster_job, branch_info)
                LibJobDsl.postclean_silverback(code_nomaster_job, project, branch_info)
                LibJobDsl.claim_builds(code_nomaster_job, branch_info)
            }
        }

        for (platform in code_stressbulkbuild_matrix) {
            for (config in platform.configs) {
                out.println("       Processing code_stressbulkbuild_matrix $platform.name $config...")
                def config_name = config
                def dry_run_config = false
                if (config instanceof Map) {
                    config_name = config.name
                    dry_run_config = config.dry_run ?: false
                }
                def code_stressbulkbuild_job_info = [
                    config            : config_name,
                    dry_run_config    : dry_run_config,
                    platform          : platform.name,
                    stressbulkbuild   : true,
                    compile_unit_tests: true,
                ]

                def code_stressbulkbuild_job = job(current_branch + '.code.stressbulkbuild.' + platform.name + '.' + config_name) {}
                freestyle_jobs.add(code_stressbulkbuild_job)
                LibCode.code_job(code_stressbulkbuild_job, project, branch_info + code_stressbulkbuild_job_info)
                if (branch_info.code_data_sync == true) {
                    LibScm.sync_code_and_data(code_stressbulkbuild_job, project, branch_info, '', '${code_changelist}')
                } else {
                    LibScm.sync_code(code_stressbulkbuild_job, project, branch_info, '${code_changelist}')
                }
                LibJobDsl.kill_processes(code_stressbulkbuild_job, branch_info)
                LibJobDsl.initialP4revert(code_stressbulkbuild_job, project, branch_info, true, false)
                LibJobDsl.addVaultSecrets(code_stressbulkbuild_job, branch_info)
                LibJobDsl.archive_non_build_logs(code_stressbulkbuild_job, branch_info)
                LibJobDsl.postclean_silverback(code_stressbulkbuild_job, project, branch_info)
                LibJobDsl.claim_builds(code_stressbulkbuild_job, branch_info)
            }
        }

        for (platform in data_matrix) {
            out.println("       Processing data_matrix $platform...")
            Boolean combine_platform = true
            def custom_tag = null
            Boolean deployment_platform = true
            Boolean enlighten_bake = false
            List<String> fb_env_values_data = []
            Boolean nightly_clean_build = false
            def platform_name = platform
            if (platform instanceof Map) {
                combine_platform = platform.combine_platform != null ? platform.combine_platform : true
                custom_tag = platform?.custom_tag
                deployment_platform = platform.deployment_platform != null ? platform.deployment_platform : true
                enlighten_bake = platform.enlighten_bake_group ?: false
                fb_env_values_data = platform.fb_env_values as List<String> ?: []
                nightly_clean_build = platform.nightly_clean_build ?: false
                platform_name = platform.name
            }

            def platform_names = []
            platform_names.addAll(patchdata_matrix.findAll { it instanceof String })
            (patchdata_matrix.findAll { it instanceof Map }).each {
                platform_names.add(it.name)
            }
            def patch_data_platform = platform_names.contains(platform_name)

            def custom_tag_job_name_suffix = ''
            if (custom_tag != null) {
                custom_tag_job_name_suffix = ".${custom_tag}"
            }

            if (branch_info.verified_data_branch == true) {
                // Generate jobs that use smoked binaries to verify data.
                def verified_data_job = job(current_branch + '.verified-data.' + branch_info.dataset + '.' + platform_name + custom_tag_job_name_suffix) {}
                freestyle_jobs.add(verified_data_job)
                LibData.data_job(verified_data_job, project, branch_info + [platform: platform_name, verified_data: true, patch_data_platform: patch_data_platform, custom_tag: custom_tag])
                LibScm.sync_code_and_data(verified_data_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.kill_processes(verified_data_job, branch_info)
                LibJobDsl.initialP4revert(verified_data_job, project, branch_info)
                LibJobDsl.addVaultSecrets(verified_data_job, branch_info)
                LibJobDsl.archive_pipelinelog(verified_data_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                LibJobDsl.archive_non_build_logs(verified_data_job, branch_info)
                LibJobDsl.postclean_silverback(verified_data_job, project, branch_info)
                LibJobDsl.agent_reboot_on_completion(verified_data_job)
            } else {
                def data_job_info = []
                if (!branch_info.deployment_data_branch && branch_info.combine_bundles && combine_platform && !branch_info.combine_bundles.combine_asset) {
                    data_job_info = [export_combine_bundles: true]
                }
                def data_job = job(current_branch + '.' + branch_info.dataset + '.' + platform_name + custom_tag_job_name_suffix) {}
                freestyle_jobs.add(data_job)
                LibData.data_job(data_job, project, branch_info + [platform: platform_name, patch_data_platform: patch_data_platform, custom_tag: custom_tag] + data_job_info)
                LibScm.sync_code_and_data(data_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.with {
                    kill_processes(data_job, branch_info)
                    initialP4revert(data_job, project, branch_info)
                    addVaultSecrets(data_job, branch_info)
                    archive_pipelinelog(data_job, branch_info.dataset, pipelineLogCodeBranch, platform_name, branch_info.archive_pipelinelog_success ?: false)
                    archive_non_build_logs(data_job, branch_info)
                    postclean_silverback(data_job, project, branch_info)
                    claim_builds(data_job, branch_info)
                    agent_reboot_on_completion(data_job)
                }
            }
            if (branch_info.deployment_data_branch && deployment_platform) {
                def data_job_info = [
                    use_super_bundles: true,
                    content_layers   : [],
                ]
                if (branch_info.combine_bundles && combine_platform && !branch_info.combine_bundles.combine_asset) {
                    data_job_info += [export_combine_bundles: true]
                }
                def deployment_data_job = job(current_branch + '.deployment-data.' + branch_info.dataset + '.' + platform_name + custom_tag_job_name_suffix) {}
                freestyle_jobs.add(deployment_data_job)
                LibData.data_job(deployment_data_job, project, branch_info + [platform: platform_name, patch_data_platform: patch_data_platform, custom_tag: custom_tag] + data_job_info)
                LibScm.sync_code_and_data(deployment_data_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.with {
                    kill_processes(deployment_data_job, branch_info)
                    initialP4revert(deployment_data_job, project, branch_info)
                    addVaultSecrets(deployment_data_job, branch_info)
                    archive_pipelinelog(deployment_data_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                    archive_non_build_logs(deployment_data_job, branch_info)
                    postclean_silverback(deployment_data_job, project, branch_info)
                    claim_builds(deployment_data_job, branch_info)
                    agent_reboot_on_completion(deployment_data_job)
                }
                // create a deployment-data job for each content layer
                for (layer in (branch_info.content_layers ?: [])) {
                    def deployment_data_layer_job = job(current_branch + '.deployment-data.' + branch_info.dataset + '.layer_' + layer + '.' + platform_name + custom_tag_job_name_suffix) {}
                    def data_layer_job_info = [
                        use_super_bundles    : true,
                        content_layers       : [layer],
                        include_default_layer: false,
                    ]
                    // TODO: do we need to export combine bundles for each layer?
                    freestyle_jobs.add(deployment_data_layer_job)
                    LibData.data_job(deployment_data_layer_job, project, branch_info + [platform: platform_name, patch_data_platform: patch_data_platform, custom_tag: custom_tag] + data_layer_job_info)
                    LibScm.sync_code_and_data(deployment_data_layer_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                    LibJobDsl.with {
                        kill_processes(deployment_data_layer_job, branch_info)
                        initialP4revert(deployment_data_layer_job, project, branch_info)
                        addVaultSecrets(deployment_data_layer_job, branch_info)
                        archive_pipelinelog(deployment_data_layer_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                        archive_non_build_logs(deployment_data_layer_job, branch_info)
                        postclean_silverback(deployment_data_layer_job, project, branch_info)
                        claim_builds(deployment_data_layer_job, branch_info)
                        agent_reboot_on_completion(deployment_data_layer_job)
                    }
                }
            }
            if (branch_info.combine_bundles && combine_platform && branch_info.combine_bundles.combine_asset && deployment_platform) {
                def data_job_info = [
                    asset                 : branch_info.combine_bundles.combine_asset,
                    custom_tag            : custom_tag,
                    export_combine_bundles: true,
                    patch_data_platform   : patch_data_platform,
                    platform              : platform_name,
                    content_layers        : [],
                ]
                def deployment_data_combine_job = job(current_branch + '.deployment-data-combine.' + branch_info.dataset + '.' + platform_name + custom_tag_job_name_suffix) {}
                freestyle_jobs.add(deployment_data_combine_job)
                LibData.data_job(deployment_data_combine_job, project, branch_info + data_job_info)
                LibScm.sync_code_and_data(deployment_data_combine_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.with {
                    kill_processes(deployment_data_combine_job, branch_info)
                    initialP4revert(deployment_data_combine_job, project, branch_info)
                    addVaultSecrets(deployment_data_combine_job, branch_info)
                    archive_pipelinelog(deployment_data_combine_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                    archive_non_build_logs(deployment_data_combine_job, branch_info)
                    postclean_silverback(deployment_data_combine_job, project, branch_info)
                    claim_builds(deployment_data_combine_job, branch_info)
                    agent_reboot_on_completion(deployment_data_combine_job)
                }
            }

            if (branch_info.export_data_branch == true) {
                if ((branch_info.statebuild_data != false && branch_info.dry_run_data != true) || branch_info.export_data_branch == true) {
                    def export_data_job = job(current_branch + '.' + branch_info.dataset + '.export-data.' + platform_name + custom_tag_job_name_suffix) {}
                    freestyle_jobs.add(export_data_job)
                    LibData.data_job(export_data_job, project, branch_info + [platform: platform_name, export_data: true, patch_data_platform: patch_data_platform, custom_tag: custom_tag])
                    LibScm.sync_code_and_data(export_data_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                    LibJobDsl.kill_processes(export_data_job, branch_info)
                    LibJobDsl.initialP4revert(export_data_job, project, branch_info)
                    LibJobDsl.addVaultSecrets(export_data_job, branch_info)
                    LibJobDsl.archive_pipelinelog(export_data_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                    LibJobDsl.archive_non_build_logs(export_data_job, branch_info)
                    LibJobDsl.postclean_silverback(export_data_job, project, branch_info)
                    LibJobDsl.agent_reboot_on_completion(export_data_job)
                }
            }

            if (branch_info.enable_clean_build_validation == true && nightly_clean_build == true) {
                def clean_data_validation_job = job(current_branch + '.' + branch_info.dataset + '.clean-data-validation.' + platform_name + custom_tag_job_name_suffix) {}
                freestyle_jobs.add(clean_data_validation_job)
                LibData.data_job(
                    clean_data_validation_job,
                    project,
                    branch_info + [
                        clean_build_validation: true,
                        platform              : platform_name,
                        use_super_bundles     : false,
                        fb_env_values_data    : fb_env_values_data,
                        custom_tag            : custom_tag,
                    ]
                )
                LibScm.sync_code_and_data(clean_data_validation_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.with {
                    initialP4revert(clean_data_validation_job, project, branch_info)
                    kill_processes(clean_data_validation_job, branch_info)
                    addVaultSecrets(clean_data_validation_job, branch_info)
                    archive_pipelinelog(clean_data_validation_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                    archive_non_build_logs(clean_data_validation_job, branch_info)
                    postclean_silverback(clean_data_validation_job, project, branch_info)
                    agent_reboot_on_completion(clean_data_validation_job)
                }
            }

            if (enlighten_bake != false) {
                for (group in platform.enlighten_bake_group) {
                    for (level in group.enlighten_bake) {
                        def verified_enlighten_job = job(current_branch + '.enlighten.' + group.group_name + '.' + branch_info.dataset + '.' + level.name + custom_tag_job_name_suffix) {}
                        freestyle_jobs.add(verified_enlighten_job)
                        enlighten_extra_data_args = [
                            '--enlighten-mode', level.mode ?: 'shelve',
                            '--p4-port', p4_data_server,
                            '--p4-client', project.p4_data_client_env,
                            '--enlighten-type', level.type ?: 'asset',
                        ]
                        if (level.filter ?: '') {
                            enlighten_extra_data_args.add('--enlighten-asset-filter')
                            enlighten_extra_data_args.add(level.filter)
                        }
                        LibData.data_job(
                            verified_enlighten_job,
                            project,
                            branch_info + [
                                asset                 : level.asset,
                                extra_data_args       : enlighten_extra_data_args,
                                platform              : platform_name,
                                use_super_bundles     : false,
                                verified_data         : true,
                                expression_debug_data : false,
                                import_avalanche_state: false,
                                export_avalanche_state: false,
                                is_enlighten_job      : true,
                                custom_tag            : custom_tag,
                            ]
                        )
                        LibScm.sync_code_and_data(verified_enlighten_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                        LibJobDsl.kill_processes(verified_enlighten_job, branch_info)
                        LibJobDsl.initialP4revert(verified_enlighten_job, project, branch_info)
                        LibJobDsl.addVaultSecrets(verified_enlighten_job, branch_info)
                        LibJobDsl.archive_pipelinelog(verified_enlighten_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                        LibJobDsl.archive_non_build_logs(verified_enlighten_job, branch_info)
                        LibJobDsl.postclean_silverback(verified_enlighten_job, project, branch_info)
                    }
                }
            }
        }

        for (platform in patchdata_matrix) {
            out.println("       Processing patchdata_matrix $platform...")
            Boolean combine_platform = true
            def platform_name = platform
            Boolean standalone_export_platform = true
            if (platform instanceof Map) {
                combine_platform = platform.combine_platform != null ? platform.combine_platform : true
                platform_name = platform.name
                standalone_export_platform = platform.standalone_export_platform != null ? platform.standalone_export_platform : true
            }
            if (!branch_info.skip_standalone_patchdata && standalone_export_platform) {
                def patchdata_job_info = [platform: platform_name]
                def move_location_bundles = branch_info.move_location_bundles ?: []
                def patchdata_job = job(current_branch + '.patchdata.' + branch_info.dataset + '.' + platform_name) {
                    for (location in move_location_bundles) {
                        publishers {
                            downstreamParameterized {
                                // Trigger these to run using the same changelists
                                trigger(current_branch + '.move_location_bundles.' + location + '.' + branch_info.dataset + '.' + platform_name) {
                                    parameters {
                                        currentBuild()
                                    }
                                }
                            }
                        }
                    }
                }
                freestyle_jobs.add(patchdata_job)
                LibData.patchdata_job(patchdata_job, project, branch_info + patchdata_job_info)
                LibScm.sync_code_and_data(patchdata_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.kill_processes(patchdata_job, branch_info)
                LibJobDsl.initialP4revert(patchdata_job, project, branch_info)
                LibJobDsl.addVaultSecrets(patchdata_job, branch_info)
                LibJobDsl.archive_pipelinelog(patchdata_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                LibJobDsl.archive_non_build_logs(patchdata_job, branch_info)
                LibJobDsl.postclean_silverback(patchdata_job, project, branch_info)
            }

            if (branch_info.combine_bundles && combine_platform) {
                def patchdata_job_info = [
                    export_combine_bundles: true,
                    platform              : platform_name,
                ]
                if (branch_info.combine_bundles.combine_asset) {
                    patchdata_job_info += [asset: branch_info.combine_bundles.combine_asset]
                }
                def move_location_bundles = branch_info.move_location_bundles ?: []
                def patchdata_combine_job = job(current_branch + '.patchdata-combine.' + branch_info.dataset + '.' + platform_name) {
                    for (location in move_location_bundles) {
                        publishers {
                            downstreamParameterized {
                                // Trigger these to run using the same changelists
                                trigger(current_branch + '.move_location_bundles.' + location + '.' + branch_info.dataset + '.' + platform_name) {
                                    parameters {
                                        currentBuild()
                                    }
                                }
                            }
                        }
                    }
                }
                freestyle_jobs.add(patchdata_combine_job)
                LibData.patchdata_job(patchdata_combine_job, project, branch_info + patchdata_job_info)
                LibScm.sync_code_and_data(patchdata_combine_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.kill_processes(patchdata_combine_job, branch_info)
                LibJobDsl.initialP4revert(patchdata_combine_job, project, branch_info)
                LibJobDsl.addVaultSecrets(patchdata_combine_job, branch_info)
                LibJobDsl.archive_pipelinelog(patchdata_combine_job, branch_info.dataset, pipelineLogCodeBranch, platform_name)
                LibJobDsl.archive_non_build_logs(patchdata_combine_job, branch_info)
                LibJobDsl.postclean_silverback(patchdata_combine_job, project, branch_info)
            }
            // Create separate combined bundles jobs if enabled
            if (branch_info.combine_bundles?.use_separate_combined_job) {
                // Add patchfrosty_matrix to branch_info only when needed for combined bundles
                def combined_branch_info = branch_info + [patchfrosty_matrix: patchfrosty_matrix]
                def combined_job_platforms = branch_info.combine_bundles?.combined_job_platforms ?: []
                for (String combined_platform in combined_job_platforms) {
                    out.println('       Processing separate combined_bundles job for platform: ' + combined_platform)
                    def combined_bundles_job = job(current_branch + '.combined_bundles.' + combined_platform) {}
                    freestyle_jobs.add(combined_bundles_job)
                    LibFrosty.combined_bundles_job(combined_bundles_job, project, combined_branch_info, combined_platform)
                    LibScm.sync_code_and_data(combined_bundles_job, project, combined_branch_info, '${data_changelist}', '${code_changelist}')
                    LibJobDsl.kill_processes(combined_bundles_job, combined_branch_info)
                    LibJobDsl.initialP4revert(combined_bundles_job, project, combined_branch_info)
                    LibJobDsl.addVaultSecrets(combined_bundles_job, combined_branch_info)
                    LibJobDsl.archive_non_build_logs(combined_bundles_job, combined_branch_info)
                    LibJobDsl.postclean_silverback(combined_bundles_job, project, combined_branch_info)
                }
            }
        }

        // Create move_location_bundles jobs for patchdata platforms if frosty_matrix is empty
        // This ensures that move_location_bundles jobs exist even when there are no frosty jobs
        def move_location_bundles = branch_info.move_location_bundles ?: []
        if (!move_location_bundles.isEmpty() && frosty_matrix.isEmpty() && frosty_for_patch_matrix.isEmpty()) {
            out.println('       Creating move_location_bundles jobs for patchdata platforms...')
            for (platform in patchdata_matrix) {
                for (location in move_location_bundles) {
                    def bundle_types = branch_info.move_location_bundles_types ?: ['bundles']
                    for (String bundleType in bundle_types) {
                        def move_location_bundles_job = job(current_branch + '.move_location_bundles.' + location + '.' + branch_info.dataset + '.' + platform.name) {}
                        freestyle_jobs.add(move_location_bundles_job)
                        LibBilbo.setup_bilbo_bundles_job_move_location(move_location_bundles_job, project, branchfile, masterSettings,
                            current_branch as String, platform.name as String, bundleType, location as String)
                        LibScm.sync_code_and_data(move_location_bundles_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                        LibJobDsl.kill_processes(move_location_bundles_job, branch_info)
                        LibJobDsl.initialP4revert(move_location_bundles_job, project, branch_info)
                        LibJobDsl.addVaultSecrets(move_location_bundles_job, branch_info)
                        LibJobDsl.archive_non_build_logs(move_location_bundles_job, branch_info)
                        LibJobDsl.postclean_silverback(move_location_bundles_job, project, branch_info)
                        LibSlack.slack_default(move_location_bundles_job, '#cobra-outage-bilbo', project.short_name as String, false)
                    }
                }
            }
        }

        def frosty_job_matrix = frosty_matrix + frosty_for_patch_matrix
        for (platform in frosty_job_matrix) {
            if (platform.name == 'ps4') {
                platform.variants.findAll { it.format == 'iso' }.each { isoConfig ->
                    if (platform.variants.find { it.format == 'digital' && it.config == isoConfig.config && it.region == isoConfig.region }) {
                        throw new CobraException('If ps4 iso is defined for a format and region combination you will get digital for ' +
                            "free so do not add both; stick to iso only. Found when processing ${branchfile}."
                        )
                    }
                }
            }

            def is_patchdata_stream = patchdata_matrix != []
            def move_location_frosty = branch_info.move_location_frosty ?: []
            for (variant in platform.variants) {
                out.println("       Processing frosty_job_matrix $platform.name $variant...")
                def frosty_job = job(current_branch + '.frosty.' + branch_info.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config) {
                    for (location in move_location_frosty) {
                        publishers {
                            downstreamParameterized {
                                // Trigger these to run using the same changelists
                                trigger(current_branch + '.move_location_frosty.' + location + '.' + branch_info.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config) {
                                    parameters {
                                        currentBuild()
                                    }
                                }
                            }
                        }
                    }
                    if (branch_info.marvin_trigger_upload_and_test && variant.format == 'files' && variant.config == 'final' && platform.name == 'win64') {
                        publishers {
                            downstreamParameterized {
                                trigger(current_branch + '.marvin.initiate') {
                                    parameters {
                                        currentBuild()
                                    }
                                }
                            }
                        }
                    }
                }
                freestyle_jobs.add(frosty_job)
                LibFrosty.frosty_job(frosty_job, project, branch_info + [platform: platform.name, is_patchdata_stream: is_patchdata_stream], variant)
                LibScm.sync_code_and_data(frosty_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.kill_processes(frosty_job, branch_info)
                LibJobDsl.initialP4revert(frosty_job, project, branch_info)
                LibJobDsl.addVaultSecrets(frosty_job, branch_info)
                LibJobDsl.archive_pipelinelog(frosty_job, branch_info.dataset, pipelineLogCodeBranch, platform.name)
                LibJobDsl.archive_non_build_logs(frosty_job, branch_info)
                LibJobDsl.postclean_silverback(frosty_job, project, branch_info)
                LibJobDsl.agent_reboot_on_completion(frosty_job)
                if (branch_info.marvin_trigger_upload == true && variant.format == 'files' && variant.config == 'final' && platform.name == 'win64') {
                    LibJobDsl.trigger_marvin_upload_on_external_jenkins(frosty_job, project, branch_info, platform.name, variant)
                }
                if (branch_info.linux_docker_images == true && variant.format == 'digital' && variant.config == 'final' && (platform.name == 'linuxserver' || platform.name == 'linux64')) {
                    LibJobDsl.trigger_gitlab_build_linux(frosty_job, branch_info, platform.name)
                }
                LibJobDsl.claim_builds(frosty_job, branch_info)

                spin_upload_matrix.each { matrixItem ->
                    def platformName = matrixItem.name
                    matrixItem.variants.each { variantItem ->
                        def format = variantItem.format
                        def config = variantItem.config
                        def region = variantItem.region

                        def jobName = "${current_branch}.spin.${platformName}.${format}.${config}.${region}"

                        def nodeLabel = LibCommonNonCps.get_setting_value(branch_info, ['spin'], 'job_label_statebuild', 'statebuild')
                        def spin_branch_info = branch_info + ['node_label': nodeLabel, 'platform': platformName, 'format': format, 'config': config, 'region': region]

                        LibJobDsl.standardPipelineJob(this, jobName, 'src/scripts/schedulers/aws/UploadToSpin.groovy').with {
                            environmentVariables(spin_branch_info)
                            parameters {
                                stringParam {
                                    name('code_changelist')
                                    defaultValue('')
                                    description('Specifies code changelist for upload.')
                                    trim(true)
                                }
                                stringParam {
                                    name('data_changelist')
                                    defaultValue('')
                                    description('Specifies data changelist for upload. Defaults to code_changelist.')
                                    trim(true)
                                }
                            }
                        }
                    }
                }

                if (variant.format.contains('steam')) {
                    def steam_job_configuration = LibCommonCps.configure_steam_upload_job(current_branch, platform.name, branch_info, variant, false)

                    LibJobDsl.standardPipelineJob(this, steam_job_configuration.job_name, 'src/scripts/schedulers/UploadToSteam.groovy').with {
                        environmentVariables(steam_job_configuration.steam_branch_info)
                        parameters {
                            stringParam {
                                name('code_changelist')
                                defaultValue('')
                                description('Specifies code changelist for upload.')
                                trim(true)
                            }
                            stringParam {
                                name('data_changelist')
                                defaultValue('')
                                description('Specifies data changelist for upload. Defaults to code_changelist.')
                                trim(true)
                            }
                            if (variant.format.contains('combine')) {
                                stringParam {
                                    name('combine_code_changelist')
                                    defaultValue('')
                                    description('Specifies code changelist from the combined stream for upload.')
                                    trim(true)
                                }
                                stringParam {
                                    name('combine_data_changelist')
                                    defaultValue('')
                                    description('Specifies data changelist from the combined stream for upload. Defaults to combine_code_changelist.')
                                    trim(true)
                                }
                            }
                        }
                    }
                }

                for (location in move_location_frosty) {
                    def move_location_frosty_job = job(current_branch + '.move_location_frosty.' + location + '.' + branch_info.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config) {}
                    freestyle_jobs.add(move_location_frosty_job)
                    LibBilbo.bilbo_frosty_job_move_location(move_location_frosty_job, project, branchfile, masterSettings, variant,
                        current_branch as String, platform.name as String, location as String)
                    LibScm.sync_code_and_data(move_location_frosty_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                    LibJobDsl.kill_processes(move_location_frosty_job, branch_info)
                    LibJobDsl.initialP4revert(move_location_frosty_job, project, branch_info)
                    LibJobDsl.addVaultSecrets(move_location_frosty_job, branch_info)
                    LibJobDsl.archive_non_build_logs(move_location_frosty_job, branch_info)
                    LibJobDsl.postclean_silverback(move_location_frosty_job, project, branch_info)
                    LibJobDsl.agent_reboot_on_completion(move_location_frosty_job)
                    LibSlack.slack_default(move_location_frosty_job, '#cobra-outage-bilbo', project.short_name as String, false)
                }

                // Create jobs to copy bundles to other locations
                for (location in move_location_bundles) {
                    def bundle_types = branch_info.move_location_bundles_types ?: ['bundles']
                    for (String bundleType in bundle_types) {
                        def move_location_bundles_job = job(current_branch + '.move_location_bundles.' + location + '.' + branch_info.dataset + '.' + platform.name) {}
                        freestyle_jobs.add(move_location_bundles_job)
                        LibBilbo.setup_bilbo_bundles_job_move_location(move_location_bundles_job, project, branchfile, masterSettings,
                            current_branch as String, platform.name as String, bundleType, location as String)
                        LibScm.sync_code_and_data(move_location_bundles_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                        LibJobDsl.kill_processes(move_location_bundles_job, branch_info)
                        LibJobDsl.initialP4revert(move_location_bundles_job, project, branch_info)
                        LibJobDsl.addVaultSecrets(move_location_bundles_job, branch_info)
                        LibJobDsl.archive_non_build_logs(move_location_bundles_job, branch_info)
                        LibJobDsl.postclean_silverback(move_location_bundles_job, project, branch_info)
                        LibSlack.slack_default(move_location_bundles_job, '#cobra-outage-bilbo', project.short_name as String, false)
                    }
                }
            }
        }

        for (platform in patchfrosty_matrix) {
            for (variant in platform.variants) {
                out.println("       Processing patchfrosty_matrix $platform.name $variant...")
                def patchfrosty_job = job(current_branch + '.patchfrosty.' + branch_info.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config) {}
                freestyle_jobs.add(patchfrosty_job)
                LibFrosty.patchfrosty_job(patchfrosty_job, project, branch_info + [platform: platform.name], variant)
                LibScm.sync_code_and_data(patchfrosty_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.kill_processes(patchfrosty_job, branch_info)
                LibJobDsl.initialP4revert(patchfrosty_job, project, branch_info)
                LibJobDsl.addVaultSecrets(patchfrosty_job, branch_info)
                LibJobDsl.archive_pipelinelog(patchfrosty_job, branch_info.dataset, pipelineLogCodeBranch, platform.name)
                LibJobDsl.archive_non_build_logs(patchfrosty_job, branch_info)
                LibJobDsl.postclean_silverback(patchfrosty_job, project, branch_info)
                LibJobDsl.agent_reboot_on_completion(patchfrosty_job)

                if (branch_info.linux_docker_images == true && variant.format == 'digital' && variant.config == 'final' && (platform.name == 'linuxserver' || platform.name == 'linux64')) {
                    LibJobDsl.trigger_gitlab_build_linux(patchfrosty_job, branch_info, platform.name)
                }

                if (variant.format.contains('steam')) {
                    def steam_job_configuration = LibCommonCps.configure_steam_upload_job(current_branch, platform.name, branch_info, variant, true)

                    LibJobDsl.standardPipelineJob(this, steam_job_configuration.job_name, 'src/scripts/schedulers/UploadToSteam.groovy').with {
                        environmentVariables(steam_job_configuration.steam_branch_info)
                        parameters {
                            stringParam {
                                name('code_changelist')
                                defaultValue('')
                                description('Specifies code changelist for upload.')
                                trim(true)
                            }
                            stringParam {
                                name('data_changelist')
                                defaultValue('')
                                description('Specifies data changelist for upload. Defaults to code_changelist.')
                                trim(true)
                            }
                            if (variant.format.contains('combine')) {
                                stringParam {
                                    name('combine_code_changelist')
                                    defaultValue('')
                                    description('Specifies code changelist from the combined stream for upload.')
                                    trim(true)
                                }
                                stringParam {
                                    name('combine_data_changelist')
                                    defaultValue('')
                                    description('Specifies data changelist from the combined stream for upload. Defaults to combine_code_changelist.')
                                    trim(true)
                                }
                            }
                        }
                    }
                }
            }
        }

        if (branch_info.shift_branch) {
            if (!frosty_matrix.isEmpty() || !frosty_for_patch_matrix.isEmpty() || !patchfrosty_matrix.isEmpty()) {
                FreeStyleJob shiftUpload = job(current_branch + '.shift.upload') {}
                freestyle_jobs.add(shiftUpload)
                out.println('       Processing shift_branch...')
                LibShift.shift_upload(shiftUpload, project, branchfile, masterSettings, current_branch)
                LibScm.sync_code(shiftUpload, project, branch_info, '${code_changelist}')
                LibJobDsl.kill_processes(shiftUpload, branch_info)
                LibJobDsl.initialP4revert(shiftUpload, project, branch_info)
                LibJobDsl.addVaultSecrets(shiftUpload, branch_info)
                LibJobDsl.archive_non_build_logs(shiftUpload, branch_info)
                LibJobDsl.postclean_silverback(shiftUpload, project, branch_info)
                if (branch_info.shift_every_build) {
                    Map slackSettings = branch_info.slack_channel_shift as Map ?: [channels: ['#cobra-outage-shift']]
                    for (channel in slackSettings.channels) {
                        boolean slackAlwaysNotify = branch_info.slack_always_notify ?: false
                        LibSlack.slack_default(shiftUpload, channel as String, project.short_name as String, slackAlwaysNotify)
                    }
                }
            }
        }

        if (branch_info.linux_docker_images == true) {
            out.println('       Processing linux_docker_images...')
            ['server', 'client'].each { platform ->
                LibJobDsl.standardPipelineJob(this, "${current_branch}.linux.${platform}.docker-image", 'src/scripts/schedulers/trigger-gitlab-pipeline.groovy').with {
                    description('Trigger a pipeline job on gitlab.ea.com for the image repository, when a (patch)frosty build succeeds (uses only the linux ' + platform + ' build from it).')
                    parameters {
                        stringParam {
                            name('code_branch')
                            defaultValue('')
                            description('Code branch for the linux ' + platform + ' build')
                            trim(true)
                        }
                        stringParam {
                            name('code_changelist')
                            defaultValue('')
                            description('Code changelist')
                            trim(true)
                        }
                        stringParam {
                            name('data_branch')
                            defaultValue('')
                            description('Data branch for the linux ' + platform + ' build')
                            trim(true)
                        }
                        stringParam {
                            name('data_changelist')
                            defaultValue('')
                            description('Data changelist')
                            trim(true)
                        }
                    }
                    environmentVariables {
                        env('platform', platform)
                    }
                }
            }
        }
        if (branch_info.custom_script) {
            branch_info.custom_script.each { String key, CustomScriptConfiguration configuration ->
                String jobName = configuration.jobName ?: key
                FreeStyleJob customScriptJob = job("${current_branch}.custom-script.${jobName}") {}
                freestyle_jobs.add(customScriptJob)
                String p4Port = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', project)
                LibCustomScript.job(customScriptJob, project, branchfile, masterSettings, current_branch, key, p4Port)
                LibScm.sync_code(customScriptJob, project, branch_info, '${code_changelist}')
                LibJobDsl.kill_processes(customScriptJob, branch_info)
                LibJobDsl.initialP4revert(customScriptJob, project, branch_info)
                LibJobDsl.addVaultSecrets(customScriptJob, branch_info)
                LibJobDsl.archive_non_build_logs(customScriptJob, branch_info)
                LibJobDsl.postclean_silverback(customScriptJob, project, branch_info)
                if (configuration.slackChannel) {
                    LibSlack.slack_default(customScriptJob, configuration.slackChannel as String, project.short_name as String, true)
                }
            }
        }
        if (branch_info.pipeline_determinism_test_configuration && !pipeline_determinism_test_matrix.isEmpty()) {
            out.println('       Processing pipeline_determinism_test_matrix...')
            def configuration = branch_info.pipeline_determinism_test_configuration
            def pipelineDeterminismTestScheduler = pipelineJob("${current_branch}.pipeline-determinism-test.start") {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/PipelineDeterminismTestScheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibPipelineDeterminismTest.start(pipelineDeterminismTestScheduler, project, branchfile, masterSettings, current_branch)
            def pipelineDeterminismBranchInfo = branch_info
            PipelineDeterminismTestConfiguration pipelineDeterminismTestConfiguration = (PipelineDeterminismTestConfiguration) branch_info.pipeline_determinism_test_configuration
            if (pipelineDeterminismTestConfiguration.perforceCodeServer && pipelineDeterminismTestConfiguration.perforceCodeCredentials &&
                pipelineDeterminismTestConfiguration.perforceDataServer && pipelineDeterminismTestConfiguration.perforceDataCredentials) {
                pipelineDeterminismBranchInfo += [
                    p4_code_creds : pipelineDeterminismTestConfiguration.perforceCodeCredentials,
                    p4_code_server: pipelineDeterminismTestConfiguration.perforceCodeServer,
                    p4_data_creds : pipelineDeterminismTestConfiguration.perforceDataCredentials,
                    p4_data_server: pipelineDeterminismTestConfiguration.perforceDataServer,
                ]
            }

            for (job_configuration in pipeline_determinism_test_matrix) {
                def job_name = 'pipeline-determinism-test'
                if (job_configuration.platform != 'win64') { // to keep things backward compatible, we don't append the win64 platform name
                    job_name = job_name + ".${job_configuration.platform}"
                }
                job_name = job_name + "${job_configuration.job_name ? ".${job_configuration.job_name}" : ''}"

                FreeStyleJob pipelineDeterminismTestJob = job("${current_branch}.${job_name}") {}
                freestyle_jobs.add(pipelineDeterminismTestJob)
                LibPipelineDeterminismTest.job(pipelineDeterminismTestJob, project, branchfile, masterSettings, current_branch, job_configuration.script_args ?: '""', job_configuration.job_label)
                LibScm.sync_code(pipelineDeterminismTestJob, project, pipelineDeterminismBranchInfo, '${code_changelist}')
                LibJobDsl.kill_processes(pipelineDeterminismTestJob, pipelineDeterminismBranchInfo)
                LibJobDsl.initialP4revert(pipelineDeterminismTestJob, project, pipelineDeterminismBranchInfo)
                LibJobDsl.addVaultSecrets(pipelineDeterminismTestJob, pipelineDeterminismBranchInfo)
                LibJobDsl.archive_non_build_logs(pipelineDeterminismTestJob, pipelineDeterminismBranchInfo)
                LibJobDsl.postclean_silverback(pipelineDeterminismTestJob, project, pipelineDeterminismBranchInfo)
                if (configuration.slackChannel) {
                    LibSlack.slack_default(pipelineDeterminismTestJob, configuration.slackChannel as String, project.short_name as String, true)
                }
            }
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, current_branch, branchfile.freestyle_job_trigger_matrix)
    }
}
