<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_folder=stage
code_branch=dev-na-to-trunk
data_folder=stage
data_branch=dev-na-to-trunk
job_label_poolbuild=poolbuild_dev-na-to-trunk
job_label_statebuild=statebuild_dev-na-to-trunk
dataset=bfdata
frostbite_licensee=BattlefieldGame
elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1
elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core
workspace_root=D:\dev
azure_elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm
azure_elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1
azure_workspace_root=E:\dev
azure_fileshare=[additional_tools_to_include:[frostedtests, win64], secret_context:glacier_azure_fileshare, target_build_share:bfglacier]
autotest_remote_settings=[eala:[credentials:monkey.bct, p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001, p4_data_creds:bct-la-p4, p4_data_server:dicela-p4edge-fb.la.ad.ea.com:2001], criterion:[p4_code_creds:perforce-battlefield-criterion, p4_code_server:oh-p4edge-fb.eu.ad.ea.com:2001, p4_data_creds:perforce-battlefield-criterion, p4_data_server:oh-p4edge-fb.eu.ad.ea.com:2001], dice:[p4_code_creds:perforce-battlefield01, p4_code_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001, p4_data_creds:perforce-battlefield01, p4_data_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001]]
report_build_version= --reporting-build-version-id %code_changelist%
webexport_script_path=Code\DICE\BattlefieldGame\fbcli\webexport.py
gametool_settings=[gametools:[icepick:[config:release, framework_args:[-G:frostbite.use-prebuilt-native-binaries=true]], frostbiteDatabaseUpgrader:[], frostyisotool:[], drone:[], framework:[], fbenv:[]]]
pipeline_determinism_test_configuration={com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration: enabled: true, referenceJob: .data.start}
deploy_frostedtests=true
deploy_tests=true
fake_ooa_wrapped_symbol=false
skip_code_build_if_no_changes=false
slack_channel_code=[channels:[#bf-dev-na-to-trunk-build-notify], skip_for_multiple_failures:true]
sndbs_enabled=true
slack_channel_data=[channels:[#dev-na-to-trunk-build-notify], skip_for_multiple_failures:true]
enable_lkg_cleaning=true
poolbuild_data=true
skip_frosty_trigger=true
timeout_hours_data=8
webexport_branch=true
webexport_allow_failure=true
poolbuild_frosty=true
slack_channel_frosty=[channels:[#dev-na-to-trunk-build-notify], skip_for_multiple_failures:true]
timeout_hours_frosty=8
use_linuxclient=true
asset=DevLevels
enable_lkg_p4_counters=true
frosty_reference_job=dev-na-to-trunk.code.start
server_asset=Game/Setup/Build/DevMPLevels
extra_data_args=[--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
extra_frosty_args=[--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
shift_branch=true
shift_every_build=true
skip_icepick_settings_file=true
strip_symbols=false
move_location_parallel=true
new_locations=[earo:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location earo --use-fbenv-core]]
cadet_activate_toolset=true
cadet_p4_fb_settings=[p4_creds:perforce-battlefield01, p4_port:dice-p4buildedge02-fb.dice.ad.ea.com:2001]
branch_name=dev-na-to-trunk
project=class com.ea.project.bct.Bct
patchfrosty_matrix=[]
node_label=statebuild_dev-na-to-trunk
platform=linuxserver
format=digital
config=final
region=ww</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist for upload.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist for upload. Defaults to code_changelist.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.aws

import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * UploadToSpin.groovy
 */
pipeline {
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.node_label)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.data_changelist, params.code_changelist])
            }
        }
        stage('Upload to Spin') {
            steps {
                P4SyncDefault(
                    project,
                    branchFile,
                    env.code_folder,
                    env.code_branch,
                    'code',
                    params.code_changelist,
                )
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                    bat([env.elipy_call, 'submit_to_spin',
                        '--code-branch', env.code_branch,
                        '--code-changelist', params.code_changelist,
                        '--data-branch', env.data_branch,
                        '--data-changelist', params.data_changelist ?: params.code_changelist,
                        '--platform', env.platform,
                        '--format', env.format,
                        '--config', env.config,
                        '--region', env.region,
                    ].join(' '))
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchFile.standard_jobs_settings?.slack_channel_spin
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>