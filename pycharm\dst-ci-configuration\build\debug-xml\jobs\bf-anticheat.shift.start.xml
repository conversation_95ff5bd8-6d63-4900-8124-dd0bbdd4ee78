<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Scheduler to shift start job for bf-anticheat</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>0</maxConcurrentPerNode>
            <maxConcurrentTotal>3</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.TimerTrigger>
                    <spec>TZ=Europe/Stockholm 
 H 0,13 * * 1-6
H 6,13 * * 7</spec>
                </hudson.triggers.TimerTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=bf-anticheat
project_name=bctch1
shift_reference_job=bf-anticheat.frosty.start</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)
/**
 * shift_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelists') {
            steps {
                script {
                    String lastCodeBuild = LibJenkins.getLastStableCodeChangelist(env.shift_reference_job as String)
                    String lastDataBuild = LibJenkins.getLastStableDataChangelist(env.shift_reference_job as String)

                    def codeChangelist = params.code_changelist ?: lastCodeBuild
                    def dataChangelist = params.data_changelist ?: lastDataBuild

                    if (codeChangelist == null || dataChangelist == null) {
                        error('Missing changelist, aborting build!')
                    }

                    def lastCodeBuilt = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME as String) ?: '1234'
                    def lastDataBuilt = LibJenkins.getLastStableDataChangelist(env.JOB_NAME as String) ?: '1234'

                    def injectMap = [
                        'code_changelist': codeChangelist,
                        'data_changelist': dataChangelist,
                        'last_code_built': lastCodeBuilt,
                        'last_data_built': lastDataBuilt,
                    ]
                    EnvInject(currentBuild, injectMap)

                    currentBuild.displayName = env.JOB_NAME + '.' + dataChangelist + '.' + codeChangelist
                }
            }
        }
        stage('Trigger shift upload') {
            steps {
                script {
                    def args = [
                        string(name: 'code_changelist', value: env.code_changelist),
                        string(name: 'data_changelist', value: env.data_changelist),
                    ]

                    if (env.code_changelist == env.last_code_built &amp;&amp; env.data_changelist == env.last_data_built) {
                        echo('Last code build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                            ' and current is CL ' + env.data_changelist + '.' + env.code_changelist + ', aborting build.')
                        currentBuild.result = Result.UNSTABLE.toString()
                    } else {
                        echo('Last build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                            ' but current is ' + env.data_changelist + '.' + env.code_changelist + ', proceeding.')
                        List&lt;JobReference&gt; jobReferences = []
                        retryOnFailureCause(3, jobReferences) {
                            String jobName = env.branch_name + '.shift.upload'
                            def shiftJob = build(job: jobName, parameters: args, propagate: false)
                            jobReferences &lt;&lt; new JobReference(downstreamJob: shiftJob, jobName: jobName, parameters: args, propagate: false)
                        }
                    }

                    if (currentBuild.result.toString() == 'SUCCESS') {
                        def shift_downstream_matrix = branchfile.shift_downstream_matrix
                        LibCommonCps.triggerDownstreamJobs(this, shift_downstream_matrix, 'shift', env.branch_name, branchfile, code_changelist, data_changelist)
                    }
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchfile.standard_jobs_settings?.slack_channel_shift ?: [channels: ['#cobra-outage-shift']]
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>