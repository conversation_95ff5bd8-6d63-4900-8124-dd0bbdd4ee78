<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_folder=tasks
code_branch=task1
data_folder=tasks
data_branch=task1
dataset=bfdata
elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core
elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1
frostbite_licensee=BattlefieldGame
workspace_root=D:\dev
azure_elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm
azure_elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1
azure_workspace_root=E:\dev
job_label_statebuild=statebuild
webexport_script_path=Code\DICE\BattlefieldGame\fbcli\webexport.py
autotest_remote_settings=[eala:[credentials:monkey.bct, p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001], dice:[p4_code_creds:perforce-battlefield01, p4_code_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001]]
skip_code_build_if_no_changes=false
slack_channel_code=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
sndbs_enabled=true
poolbuild_data=true
webexport_branch=true
webexport_allow_failure=true
poolbuild_frosty=true
slack_channel_frosty=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
timeout_hours_frosty=5
use_linuxclient=true
asset=Task1ClientLevels
enable_lkg_p4_counters=true
extra_data_args=[--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 10 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false ]
extra_frosty_args=[--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 10 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
skip_icepick_settings_file=true
server_asset=Task1ServerLevels
shift_branch=true
shift_every_build=true
strip_symbols=false
move_location_parallel=true
new_locations=[Guildford:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location Guildford --use-fbenv-core], Montreal:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location Montreal --use-fbenv-core], RippleEffect:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location RippleEffect --use-fbenv-core]]
branch_name=task1
project=class com.ea.project.bct.Bct
patchfrosty_matrix=[]
node_label=statebuild
platform=linuxserver
format=digital
config=final
region=playtest</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist for upload.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist for upload. Defaults to code_changelist.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.aws

import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * UploadToSpin.groovy
 */
pipeline {
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.node_label)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.data_changelist, params.code_changelist])
            }
        }
        stage('Upload to Spin') {
            steps {
                P4SyncDefault(
                    project,
                    branchFile,
                    env.code_folder,
                    env.code_branch,
                    'code',
                    params.code_changelist,
                )
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                    bat([env.elipy_call, 'submit_to_spin',
                        '--code-branch', env.code_branch,
                        '--code-changelist', params.code_changelist,
                        '--data-branch', env.data_branch,
                        '--data-changelist', params.data_changelist ?: params.code_changelist,
                        '--platform', env.platform,
                        '--format', env.format,
                        '--config', env.config,
                        '--region', env.region,
                    ].join(' '))
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchFile.standard_jobs_settings?.slack_channel_spin
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>