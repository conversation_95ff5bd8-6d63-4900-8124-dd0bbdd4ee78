# LibFrosty.groovy Changes Explanation

## Overview

This document explains the changes made to `LibFrosty.groovy` and `frosty_start.groovy` to fix delta bundle creation for first patch builds. The changes address the fundamental difference between how first patch builds and non-first patch builds handle baseline references for delta bundle creation.

## Key Concepts

### Disc Baseline vs Patch Baseline

**Disc Baseline**: The original "gold master" or "disc" version of the game that was shipped to customers. This represents the baseline that all patches are built against.

**Patch Baseline**: The previous patch version that serves as the baseline for creating the next incremental patch.

### First Patch vs Non-First Patch

**First Patch**: The very first patch after the disc/gold master release. It compares against the disc baseline to create delta bundles.

**Non-First Patch**: Subsequent patches that compare against the previous patch baseline to create incremental delta bundles.

## Changes in LibFrosty.groovy

### Lines 530-544: Parameter Definition for First Patch

```groovy
if (create_delta_bundles) {
    if (branch_info.first_patch) {
        stringParam {
            name('disc_code_branch')
            defaultValue('')
            description('Specifies disc baseline code branch for delta bundle creation.')
            trim(true)
        }
        stringParam {
            name('disc_data_branch')
            defaultValue('')
            description('Specifies disc baseline data branch for delta bundle creation.')
            trim(true)
        }
    }
}
```

**Purpose**: For first patch builds, we need to define parameters that will receive the actual disc baseline branch names from the baseline file. These parameters are populated by the `ReturnDiscBaseline` function.

**Why First Patch Only**: Non-first patches use the current branch as their disc baseline reference, so they don't need these dynamic parameters.

### Lines 581-609: Command Construction Logic

```groovy
// Add delta bundle parameters if needed
if (create_delta_bundles) {
    if (branch_info.first_patch) {
        command_parts.addAll([
            '--disc-code-branch', '%disc_code_branch%',
            '--disc-code-changelist', '%disc_code_changelist%',
            '--disc-data-branch', '%disc_data_branch%',
            '--disc-data-changelist', '%disc_data_changelist%',
        ])
    } else {
        command_parts.addAll([
            '--disc-code-branch', branch_info.code_branch,
            '--disc-code-changelist', '%disc_code_changelist%',
            '--disc-data-branch', branch_info.data_branch,
            '--disc-data-changelist', '%disc_data_changelist%',
        ])
    }
}
```

**First Patch Logic**: Uses parameterized values (`%disc_code_branch%`, `%disc_data_branch%`) that are populated at runtime from the baseline file. This allows the first patch to reference the actual disc baseline branches, which may be different from the current branch.

**Non-First Patch Logic**: Uses the current branch (`branch_info.code_branch`, `branch_info.data_branch`) as the disc baseline. This is because for subsequent patches, the "disc baseline" is effectively the current branch's baseline state.

## Changes in frosty_start.groovy

### Lines 142-151: Baseline Parameter Population

```groovy
// Add baseline parameters for delta bundle creation in frosty
def platform_modifiers = [platform]
def baseline_set = LibCommonNonCps.get_setting_value(branchfile.standard_jobs_settings, platform_modifiers, 'baseline_set', true)
if (baseline_set) {
    baseline_file = ReturnBaselineFile(project.name)
    def use_combined_baseline = branchfile.standard_jobs_settings.combine_bundles?.is_target_branch ?: false
    def baseline_args = ReturnDiscBaseline(baseline_file, env.branch_name, platform, use_combined_baseline)
    combined_job_args += baseline_args
}
```

**Purpose**: This code populates the disc baseline parameters that were defined in LibFrosty.groovy. It:

1. Checks if baseline is configured for this platform
2. Loads the baseline file for the project
3. Calls `ReturnDiscBaseline` to get the actual disc baseline branches and changelists
4. Adds these parameters to the job arguments

**Why This is Needed**: The `ReturnDiscBaseline` function looks up the actual disc baseline configuration from the baseline file and returns the correct branch names and changelists that should be used for delta bundle creation.

## Supporting Evidence from Codebase

### 1. ReturnDiscBaseline Function
The `ReturnDiscBaseline.groovy` function shows how disc baselines are resolved:

```groovy
def baseline = baseline_file.get_disc_baseline_for(branch_name, platform)
def platform_args = [
    string(name: 'disc_code_branch', value: baseline.code_branch),
    string(name: 'disc_data_branch', value: baseline.data_branch),
    string(name: 'disc_code_changelist', value: baseline.code_changelist),
    string(name: 'disc_data_changelist', value: baseline.data_changelist),
]
```

### 2. Combined Bundles Script Validation
In `combined_bundles.py`, the script validates that disc baseline parameters are required for delta bundle creation:

```python
if create_delta_bundles:
    if (
        not disc_code_branch
        or not disc_code_changelist
        or not disc_data_branch
        or not disc_data_changelist
    ):
        raise ELIPYException(
            "When creating delta bundles, disc baseline parameters are required: "
            "--disc-code-branch, --disc-code-changelist, --disc-data-branch, "
            "--disc-data-changelist"
        )
```

### 3. First Patch vs Non-First Patch in Data Builds
In `LibData.groovy`, we see similar logic for data builds:

```groovy
if (first_patch == true) {
    extra_args += ' --first-patch'
    if (standalone_disc_baseline) {
        extra_args += ' --standalone-baseline'
    }
} else {
    extra_args += ' --patch-code-branch %patch_code_branch% --patch-code-changelist %patch_code_changelist%'
    extra_args += ' --patch-data-branch %patch_data_branch% --patch-data-changelist %patch_data_changelist%'
}
```

### 4. Branch Configuration Example
The `CH1_content_dev_first_patch.groovy` shows a real first patch configuration:

```groovy
first_patch: true,
fetch_baseline_reference_job: 'CH1-content-dev-disc-build.store_regular_baseline.start',
use_dynamic_disc_baselines: true,
```

## Why This Design Makes Sense

1. **First Patch Scenario**: 
   - The first patch needs to create delta bundles against the original disc/gold master
   - The disc baseline may be from a completely different branch (e.g., a release branch)
   - The baseline file contains the authoritative mapping of which branches/changelists represent the disc baseline

2. **Non-First Patch Scenario**:
   - Subsequent patches build incrementally on top of previous patches
   - The current branch already represents the appropriate baseline state
   - No need to look up external baseline references

3. **Flexibility**:
   - The baseline file can be updated to point to different disc baselines without changing job configurations
   - Dynamic disc baselines allow for automatic updates based on successful builds

This design ensures that delta bundles are created with the correct baseline references, enabling proper incremental patching workflows for game development.
