<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_folder=tasks
code_branch=ecs-splines
data_folder=tasks
data_branch=ecs-splines
dataset=bfdata
elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core
elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1
frostbite_licensee=BattlefieldGame
workspace_root=D:\dev
azure_elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm
azure_elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1
azure_workspace_root=E:\dev
azure_fileshare=[additional_tools_to_include:[frostedtests, win64], secret_context:glacier_azure_fileshare, target_build_share:bfglacier]
deploy_frostedtests=true
deploy_tests=true
skip_code_build_if_no_changes=false
slack_channel_code=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
fake_ooa_wrapped_symbol=false
statebuild_code=false
sndbs_enabled=true
asset=DevLevels
skip_icepick_settings_file=true
strip_symbols=false
move_location_parallel=true
new_locations=[RippleEffect:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location RippleEffect --use-fbenv-core]]
branch_name=ecs-splines
project=class com.ea.project.bct.Bct
patchfrosty_matrix=[]
platform=tool
config=release
content_type=code
additional_tools_to_include=frostedtests, win64
target_build_share=bfglacier
secret_context=glacier_azure_fileshare
node_label=copy_build_to_azure</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist for upload.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>source</name>
                    <description>Skip path logic and use this path as source</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>destination</name>
                    <description>Skip path logic and use this path as destination</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>target_build_share</name>
                    <description>elipy config key to find buildshare in alternate_build_shares.</description>
                    <defaultValue>bfglacier</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>secret_context</name>
                    <description>Elipy config secrets where key for filer auth</description>
                    <defaultValue>glacier_azure_fileshare</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>additional_tools_to_include</name>
                    <description>Additional tool(s) to pull from network share</description>
                    <defaultValue>frostedtests, win64</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.project.GetMasterFile
import com.ea.project.GetBranchFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * azure_upload_scheduler.groovy
 */
pipeline {
    agent {
        node {
            label(env.node_label)
            customWorkspace(project.workspace_root)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.code_changelist])
            }
        }
        stage('Upload to Azure') {
            steps {
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                script {
                    String additionalTools = params.additional_tools_to_include
                    def optional_args = ''
                    optional_args += params.source ? ' --source ' + params.source : ''
                    optional_args += params.destination ? ' --destination ' + params.destination : ''
                    optional_args += additionalTools ? additionalTools.split(',').collect { " --additional-tools-to-include ${it.trim()}" }.join('') : ''
                    withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                        bat([env.elipy_call, 'copy_from_filer_to_azure',
                             '--content-type', env.content_type,
                             '--platform', env.platform,
                             '--config', env.config,
                             '--code-branch', env.code_branch,
                             '--code-changelist', params.code_changelist,
                             '--target-build-share', params.target_build_share,
                             '--secret-context', params.secret_context,
                             '--licensee', env.frostbite_licensee,
                             optional_args,
                        ].join(' '))
                    }
                }
            }
        }
    }
}

</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>