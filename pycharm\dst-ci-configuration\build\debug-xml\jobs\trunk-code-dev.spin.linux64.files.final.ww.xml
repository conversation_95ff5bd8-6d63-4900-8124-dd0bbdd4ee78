<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_folder=mainline
code_branch=trunk-code-dev
data_folder=mainline
data_branch=trunk-code-dev
koala_code=true
job_label_poolbuild=poolbuild_trunk-code-dev
job_label_statebuild=statebuild
dataset=bfdata
elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core
elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1
frostbite_licensee=BattlefieldGame
workspace_root=D:\dev
azure_elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm
azure_elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1
azure_workspace_root=E:\dev
webexport_script_path=Code\DICE\BattlefieldGame\fbcli\webexport.py
azure_fileshare=[additional_tools_to_include:[frostedtests, win64], secret_context:glacier_azure_fileshare, target_build_share:bfglacier]
gametool_settings=[gametools:[icepick:[config:release, framework_args:[-G:frostbite.use-prebuilt-native-binaries=true]], frostbiteDatabaseUpgrader:[], frostyisotool:[], drone:[], framework:[], fbenv:[]]]
coverity_settings=[credentials:monkey.bct, ess_secrets_credential:bct-secrets-secret-id, ess_secrets_key:BCT_SECRETS_SECRET_ID, run_coverity:true, trigger:@daily, job_label:bct_coverity, artifactory_source_path:bctinfrax-bctinfraxgeneric-generic-federated/tools/coverity/cov-analysis-win64-2024.12.0.zip, p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001]
autotest_remote_settings=[eala:[credentials:monkey.bct, ess_secrets_credential:bct-secrets-secret-id, ess_secrets_key:BCT_SECRETS_SECRET_ID, p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001], criterion:[p4_code_creds:perforce-battlefield-criterion, p4_code_server:oh-p4edge-fb.eu.ad.ea.com:2001, p4_data_creds:perforce-battlefield-criterion, p4_data_server:oh-p4edge-fb.eu.ad.ea.com:2001], dice:[p4_code_creds:perforce-battlefield01, p4_code_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001]]
pipeline_determinism_test_configuration={com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration: enabled: true, referenceJob: .data.start}
custom_script=[portal-make-sdk:{com.ea.lib.model.branchsettings.CustomScriptConfiguration: enabled: true, command: D:\dev\Python\virtual\Scripts\python.exe Code\DICE\BattlefieldGame\Scripts\portal_make_sdk.py}]
deploy_frostedtests=true
deploy_tests=true
fake_ooa_wrapped_symbol=false
skip_code_build_if_no_changes=false
slack_channel_code=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
statebuild_code=false
report_build_version= --reporting-build-version-id %code_changelist%
sndbs_enabled=true
deployment_data_branch=true
enable_lkg_cleaning=true
poolbuild_data=true
slack_channel_data=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
statebuild_data=false
statebuild_webexport=false
webexport_allow_failure=true
webexport_branch=true
frosty_reference_job=trunk-code-dev.deployment-data.start
poolbuild_frosty=true
slack_channel_frosty=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
use_linuxclient=true
asset=DevLevels
custom_tests=[custom_configs:[trunk-code-dev.json, trunk-code-dev-ade.json]]
retry_limit_data=1
deployment_data_reference_job=trunk-code-dev.data.start
enable_lkg_p4_counters=true
enable_eac=true
environment_variables=[ASAN_WIN_CONTINUE_ON_INTERCEPTION_FAILURE:1]
extra_data_args=[--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
extra_frosty_args=[--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
extra_icepick_args=--heartbeat-timeout 600
remote_masters_to_receive_code=[[name:bct-preflight-jenkins.cobra.dre.ea.com, allow_failure:false]]
remote_masters_to_receive_data=[[name:bct-preflight-jenkins.cobra.dre.ea.com, allow_failure:false]]
prebuild_info=[config:release, input_param_path:Code\DICE\BattlefieldGame\BattlefieldGame-outsource-input-param.xml, platforms_sln:[tool, frosted], platform_prebuild:[win64-dll], platform_validation:[tool, frosted], prebuild_path://fblicensee/battlefield/trunk-code-dev-outsource-prebuilts, skip_platforms:[gdk, NX, ps4, ps5, xdk], outsource_validation:true, extra_args:[]]
server_asset=Game/Setup/Build/DevMPLevels
shift_branch=true
shift_reference_job=trunk-code-dev.frosty.start
shift_subscription_matrix=[[dest_location:RippleEffect, build_type:offsite_basic_drone, job_label:bct_eala &amp;&amp; shift &amp;&amp; processing, p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001, p4_data_server:dicela-p4edge-fb.la.ad.ea.com:2001, src_location:DiceStockholm, trigger_type:none]]
single_stream_smoke=true
skip_icepick_settings_file=true
smoke_downstream_job=trunk-code-dev.integrate-upgrade-to.trunk-content-dev.start,trunk-code-dev.integrate-upgrade-to.trunk-to-dev-na.start
strip_symbols=false
timeout_hours_data=6
move_location_parallel=true
new_locations=[earo:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location earo --use-fbenv-core], Montreal:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location Montreal --use-fbenv-core], RippleEffect:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location RippleEffect --use-fbenv-core]]
branch_name=trunk-code-dev
project=class com.ea.project.bct.Bct
patchfrosty_matrix=[]
node_label=statebuild
platform=linux64
format=files
config=final
region=ww</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist for upload.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist for upload. Defaults to code_changelist.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.aws

import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * UploadToSpin.groovy
 */
pipeline {
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.node_label)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.data_changelist, params.code_changelist])
            }
        }
        stage('Upload to Spin') {
            steps {
                P4SyncDefault(
                    project,
                    branchFile,
                    env.code_folder,
                    env.code_branch,
                    'code',
                    params.code_changelist,
                )
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                    bat([env.elipy_call, 'submit_to_spin',
                        '--code-branch', env.code_branch,
                        '--code-changelist', params.code_changelist,
                        '--data-branch', env.data_branch,
                        '--data-changelist', params.data_changelist ?: params.code_changelist,
                        '--platform', env.platform,
                        '--format', env.format,
                        '--config', env.config,
                        '--region', env.region,
                    ].join(' '))
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchFile.standard_jobs_settings?.slack_channel_spin
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>