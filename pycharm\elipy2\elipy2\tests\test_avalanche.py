"""
test_avalanche.py
"""
import json
import winreg

import mock
import os
import pytest
from mock import call, MagicMock, mock_open, patch
from past.builtins import basestring
import elipy2
from elipy2 import build_metadata
from elipy2.exceptions import AvalancheException, ELIPYException, WindowsServiceException
from elipy2.tests.testutils import patch_temp_dir, patch_winreg


def get_mock_all_db_data():
    data = [
        {"updated": "2018-05-01T07:11:34Z", "id": "production"},
        {"updated": "2018-05-02T07:11:34Z", "id": "other"},
        {"updated": "2018-05-03T07:11:34Z", "id": "temp_56523"},
        {"updated": "2018-05-04T07:11:34Z", "id": "temp_23"},
        {"updated": "2018-05-05T07:11:34Z", "id": "temp_1_ps4"},
        {"updated": "2018-05-06T07:11:34Z", "id": "temp_2_xb1"},
        {"updated": "2018-05-07T07:11:34Z", "id": "temp_56523"},
        {"updated": "2018-05-08T07:11:34Z", "id": "temp_23"},
        {"updated": "2018-05-09T07:11:34Z", "id": "temp_1_ps4"},
        {"updated": "2018-05-11T07:11:34Z", "id": "temp_2_xb1"},
        {"updated": "2018-05-12T07:11:34Z", "id": "temp_23"},
        {"updated": "2018-05-13T07:11:34Z", "id": "temp_1_ps4"},
        {"updated": "2018-05-14T07:11:34Z", "id": "temp_2_xb1"},
        {"updated": "2018-05-15T07:11:34Z", "id": "temp_23"},
        {"updated": "2018-05-16T07:11:34Z", "id": "temp_1_ps4"},
        {"updated": "2018-05-17T07:11:34Z", "id": "temp_2_xb1"},
        {"updated": "2018-05-18T07:11:34Z", "id": "temp_23"},
        {"updated": "2018-05-19T07:11:34Z", "id": "temp_23"},
    ]
    return data


class MockResponse(object):
    def __init__(self):
        self.read_count = 0

    def __enter__(self):
        return self

    def __exit__(self, _u, _n, _used):
        return

    status_code = 200

    @staticmethod
    def json():
        return {"databases": [], "summary": "Running"}

    @staticmethod
    def raise_for_status():
        return

    @staticmethod
    def iter_content(_):
        yield [i for i in range(3)]

    def read(self):
        self.read_count += 1
        return json.dumps(get_mock_all_db_data()).encode("utf-8")


elipy_test_avalanche_config = elipy2.config.ConfigManager(
    path=os.path.join(os.path.dirname(__file__), "data", "elipy_test_avalanche.yml"),
    default_location="default",
)


@patch("elipy2.avalanche.SETTINGS", elipy_test_avalanche_config)
@pytest.mark.usefixtures(patch_temp_dir.__name__)
@pytest.mark.usefixtures("mock_avalanche")
@patch("elipy2.local_paths.get_avalanchecli_exe_path", MagicMock(return_value="avalanche_path"))
class TestAvalanche(object):
    def setup(self):
        self.patcher_run = patch("elipy2.core.run")
        self.mock_run = self.patcher_run.start()
        self.mock_run.return_value = (0, [], [])

        self.patcher_mkdirs = patch("os.makedirs")
        self.mock_mkdirs = self.patcher_mkdirs.start()
        self.mock_mkdirs.return_value = True

        self.patcher_get_computer_name = patch("elipy2.windows_tools.get_computer_name")
        self.mock_get_computer_name = self.patcher_get_computer_name.start()
        self.mock_get_computer_name.return_value = "fakeymcfake-node-01"

        self.patcher_ensure_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_config = self.patcher_ensure_config.start()

        self.patcher_process_kill = patch("elipy2.running_processes.kill")
        self.mock_process_kill = self.patcher_process_kill.start()

        self.patcher_query_service = patch("elipy2.windows_tools.query_service")
        self.mock_query_service = self.patcher_query_service.start()

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

        self.patcher_index = patch("elipy2.data.DataUtils.run_indexing")
        self.mock_index = self.patcher_index.start()

    def teardown(self):
        patch.stopall()

    def test__http_get(self):
        url = "http://test-url"
        data = elipy2.avalanche._http_get("http://test-url")
        assert data["text"] == url

    @patch("elipy2.avalanche._http_put")
    def test_put_built_levels(self, mock__http_put):
        elipy2.avalanche.put_built_levels("some_db", json.dumps(["data", "data2"]))
        mock__http_put.assert_called_once_with(
            "http://localhost:1338/db/some_db/kv/drone/builtlevels", '["data", "data2"]'
        )

    def test__http_delete(self):
        url = "http://test-url"
        data = elipy2.avalanche._http_delete("http://test-url")
        assert data["text"] == url

    @patch("requests.delete")
    def test__http_delete_bad_status(self, mock_delete):
        mock_delete.status_code = 999
        with pytest.raises(ELIPYException):
            elipy2.avalanche._http_delete("http://test-url")

    def test__http_get_bad_status(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche._http_get("http://test-url/bad_error_code")

    def test_getdbs(self):
        assert elipy2.avalanche.getdbs(avalanche_url="http://localhost_empty:1338/") == []

    @patch("elipy2.windows_tools.shutdown_service", MagicMock())
    @patch("elipy2.windows_tools.start_service", MagicMock())
    def test_nuke(self):
        elipy2.avalanche.nuke()
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "nuke", "-y"],
            print_std_out=True,
        )

    @patch("elipy2.windows_tools.shutdown_service", MagicMock())
    @patch("elipy2.windows_tools.start_service", MagicMock())
    def test_nuke_exception(self):
        with patch("elipy2.core.run", MagicMock(side_effect=Exception("test exception"))):
            with pytest.raises(Exception):
                elipy2.avalanche.nuke()

    @patch("elipy2.windows_tools.shutdown_service", MagicMock())
    @patch("elipy2.windows_tools.start_service", MagicMock())
    @patch("elipy2.avalanche.getdbs")
    @patch("elipy2.core.run")
    def test_nuke_exception_not_failing(self, mock_run, mock_getdbs):
        mock_run.side_effect = Exception()
        mock_getdbs.return_value = []
        elipy2.avalanche.nuke()

    @patch("elipy2.windows_tools.shutdown_service", MagicMock())
    @patch("elipy2.windows_tools.start_service", MagicMock())
    @patch("elipy2.avalanche.getdbs")
    @patch("elipy2.core.run")
    def test_nuke_exception_failure_dbs_found(self, mock_run, mock_getdbs):
        mock_run.side_effect = Exception()
        mock_getdbs.return_value = [{"some": "db"}]
        with pytest.raises(Exception):
            elipy2.avalanche.nuke()

    @patch("elipy2.windows_tools.shutdown_service", MagicMock())
    @patch("elipy2.windows_tools.start_service", MagicMock())
    @patch("elipy2.windows_tools.query_service")
    @patch("time.sleep")
    def test_nuke_timeout(self, mock_sleep, mock_query_service):
        mock_query_service.side_effect = [
            "Unknown",
            "Unknown",
            "Unknown",
            "Unknown",
            "Unknown",
            "Running",
        ]
        elipy2.avalanche.nuke()
        assert mock_sleep.call_count == 5

    def test_clean_avalanche(self):
        elipy2.avalanche.clean_avalanche("name")
        self.mock_run.assert_called_once_with(
            ["avalanchecli.exe", "clean", "name"],
            print_std_out=True,
        )

    def test_clone_db(self):
        elipy2.avalanche.clone_db("first_database", "second_database")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "clone", "first_database", "second_database"],
            print_std_out=True,
        )

    def test_drop(self):
        elipy2.avalanche.drop("some-db")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "drop", "some-db"],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.os.path.exists")
    @patch("elipy2.avalanche.core.delete_folder")
    def test_ddelta_is_path(self, mock_delete, mock_exists):
        mock_exists.return_value = True
        elipy2.avalanche.ddelta("head-db-name", "base-db-name", "out-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "ddelta", "head-db-name", "base-db-name", "out-path"],
            print_std_out=True,
        )
        assert mock_delete.call_count == 1

    def test_ddelta(self):
        elipy2.avalanche.ddelta("head-db-name", "base-db-name", "out-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "ddelta", "head-db-name", "base-db-name", "out-path"],
            print_std_out=True,
        )

    def test_export(self):
        self.mock_run.return_value = (0, [""], [""])
        elipy2.avalanche.export("some-state", "some-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "export", "some-state", "some-path"],
            print_std_out=True,
            capture_std_out=True,
        )

    def test_export_noobject_scenario_1(self):
        self.mock_run.return_value = (
            0,
            [""],
            ["failed.  (HTTP response: 404, error: 0, message: 'Object not found')"],
        )
        with pytest.raises(AvalancheException):
            elipy2.avalanche.export("some-state", "some-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "export", "some-state", "some-path"],
            print_std_out=True,
            capture_std_out=True,
        )

    def test_export_noobject_scenario_2(self):
        """
        https://electronic-arts.slack.com/archives/CP0R7L6PR/p1666096990775329
        https://jaas.ea.com/browse/COBRA-467
        """
        self.mock_run.return_value = (
            0,
            [""],
            ["Failed to retrieve SHA1 2c48841da2842559cf310fda3112041f62effaa5"],
        )
        with pytest.raises(AvalancheException):
            elipy2.avalanche.export("some-state", "some-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "export", "some-state", "some-path"],
            print_std_out=True,
            capture_std_out=True,
        )

    def test_export_caught_exception(self):
        self.mock_run.return_value = (
            0,
            [""],
            ["Caught exception in export: some kind of exception"],
        )
        with pytest.raises(AvalancheException):
            elipy2.avalanche.export("some-state", "some-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "export", "some-state", "some-path"],
            print_std_out=True,
            capture_std_out=True,
        )

    @patch("elipy2.avalanche.os.path.exists")
    @patch("elipy2.avalanche.core.delete_folder")
    def test_export_is_path(self, mock_delete, mock_exists):
        self.mock_run.return_value = (0, [""], [""])
        mock_exists.return_value = True
        elipy2.avalanche.export("some-state", "some-path")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "export", "some-state", "some-path"],
            print_std_out=True,
            capture_std_out=True,
        )
        assert mock_delete.call_count == 1

    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    def test_import_baselinedb(self):
        elipy2.avalanche.import_baselinedb("some-path", "some-db")
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "import",
                "baseline_some-db",
                "some-path",
            ],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("elipy2.avalanche.drop", MagicMock())
    def test_import_baselinedb_cleanup_stop(self):
        elipy2.avalanche.import_baselinedb(
            "some-path",
            "BattlefieldGameData.kin-dev.Win32.Debug",
            avalanche_url="http://localhost_baseline:1338/",
        )

        assert not self.mock_run.called

    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("elipy2.avalanche.drop")
    def test_import_baselinedb_cleanup_continue(self, mock_drop):
        elipy2.avalanche.import_baselinedb(
            "some-path",
            "BattlefieldGameData.kin-dev.Win32.Debug",
            postfix="_234_345",
            avalanche_url="http://localhost_import:1338/",
        )
        mock_drop.assert_called_once()
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "import",
                "baseline_BattlefieldGameData.kin-dev.Win32.Debug_234_345",
                "some-path",
            ],
            print_std_out=True,
        )

    def test_importdb(self):
        elipy2.avalanche.importdb("some-path", "some-db-name")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "import", "some-db-name", "some-path"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_with_source(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = False
        elipy2.avalanche.remote_clone_db("db1", "db2", "dest", "source")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "remoteclone", "db1@source", "db2@dest"],
            print_std_out=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_ephemeral_default(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = True
        elipy2.avalanche.remote_clone_db("db1", "db2", "dest", "source", limited_lifetime=True)
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "remoteclone",
                "db1@source",
                "db2@dest",
                "--ephemeralLifetime",
                str(5 * 24),
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_ephemeral_no_setting(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = True
        elipy2.avalanche.remote_clone_db(
            "db1", "db2", "dest", "source", branch="some_branch", limited_lifetime=True
        )
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "remoteclone",
                "db1@source",
                "db2@dest",
                "--ephemeralLifetime",
                str(5 * 24),
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_ephemeral_with_setting(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = True
        elipy2.avalanche.remote_clone_db(
            "db1", "db2", "dest", "source", branch="heavy", limited_lifetime=True
        )
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "remoteclone",
                "db1@source",
                "db2@dest",
                "--ephemeralLifetime",
                str(1 * 24),
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_ephemeral_with_bad_setting(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = True
        elipy2.avalanche.remote_clone_db(
            "db1", "db2", "dest", "source", branch="error", limited_lifetime=True
        )
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "remoteclone",
                "db1@source",
                "db2@dest",
                "--ephemeralLifetime",
                str(5 * 24),
            ],
            print_std_out=True,
        )

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_complete_clone(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = False
        elipy2.avalanche.remote_clone_db("db1", "db2", "dest", "source", complete_clone=True)
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "remoteclone", "db1@source", "db2@dest", "--complete"],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.get_built_levels")
    @patch("elipy2.avalanche.put_built_levels")
    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_push_built_levels(
        self, mock_minimum_fb_version, mock_put_built_levels, mock_get_built_levels
    ):
        mock_minimum_fb_version.return_value = False
        mock_get_built_levels.return_value = [{"LevelKey": "LevelData"}]
        elipy2.avalanche.remote_clone_db("db1", "db2", "dest", "source", push_built_levels=True)
        assert mock_put_built_levels.call_count == 1

    @patch("elipy2.avalanche.os.makedirs", MagicMock())
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_new_dest(self, mock_os_path_exists):
        def side_effect(arg):
            if arg == "some-path":
                return False
            return True

        mock_os_path_exists.side_effect = side_effect
        platforms_to_test = [
            "xb1",
            "ps4",
            "pc",
            "win64",
            "win64game",
            "win32",
            "linux",
            "win64server",
            "server",
            "linuxserver",
            "linux64",
            "gen4a",
            "gen4b",
            "dedicatedserver",
            "nx",
        ]

        for platform in platforms_to_test:
            avalanche_platform_name = elipy2.avalanche.get_avalanche_platform_name(platform)
            elipy2.avalanche.deploy("some-db", platform, "some-path")
            self.mock_run.assert_called_with(
                [
                    elipy2.local_paths.get_avalanchecli_exe_path(),
                    "deploy",
                    "some-db",
                    "some-path",
                    avalanche_platform_name,
                    "breadthFirst",
                ],
                print_std_out=True,
            )

    @patch("elipy2.avalanche.os.makedirs", MagicMock())
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_new_dest_extra_args(self, mock_os_path_exists):
        def side_effect(arg):
            if arg == "some-path":
                return False
            return True

        mock_os_path_exists.side_effect = side_effect
        platforms_to_test = [
            "xb1",
            "ps4",
            "pc",
            "win64",
            "win64game",
            "win32",
            "linux",
            "win64server",
            "server",
            "linuxserver",
            "linux64",
            "gen4a",
            "gen4b",
            "dedicatedserver",
            "nx",
        ]

        for platform in platforms_to_test:
            avalanche_platform_name = elipy2.avalanche.get_avalanche_platform_name(platform)
            elipy2.avalanche.deploy("some-db", platform, "some-path", extra_args=["extra", "args"])
            self.mock_run.assert_called_with(
                [
                    elipy2.local_paths.get_avalanchecli_exe_path(),
                    "deploy",
                    "some-db",
                    "some-path",
                    avalanche_platform_name,
                    "breadthFirst",
                    "extra",
                    "args",
                ],
                print_std_out=True,
            )

    @patch("elipy2.avalanche.os.makedirs", MagicMock())
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_non_default_ordering_algorithm(self, mock_os_path_exists):
        def side_effect(arg):
            if arg == "some-path":
                return False
            return True

        mock_os_path_exists.side_effect = side_effect

        elipy2.avalanche.deploy("some-db", "ps5", "some-path", ordering_algorithm="some_algorithm")
        self.mock_run.assert_called_once_with(
            [
                elipy2.local_paths.get_avalanchecli_exe_path(),
                "deploy",
                "some-db",
                "some-path",
                "Ps5",
                "some_algorithm",
            ],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.os.makedirs", MagicMock())
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_no_ordering_algorithm(self, mock_os_path_exists):
        def side_effect(arg):
            if arg == "some-path":
                return False
            return True

        mock_os_path_exists.side_effect = side_effect

        elipy2.avalanche.deploy("some-db", "ps5", "some-path", ordering_algorithm=None)
        self.mock_run.assert_called_once_with(
            [
                elipy2.local_paths.get_avalanchecli_exe_path(),
                "deploy",
                "some-db",
                "some-path",
                "Ps5",
            ],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.os.makedirs", MagicMock())
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_skip_platform(self, mock_os_path_exists):
        def side_effect(arg):
            if arg == "some-path":
                return False
            return True

        mock_os_path_exists.side_effect = side_effect

        elipy2.avalanche.deploy(
            "some-db", "ps5", "some-path", include_platform=False, ordering_algorithm=None
        )
        self.mock_run.assert_called_once_with(
            [
                elipy2.local_paths.get_avalanchecli_exe_path(),
                "deploy",
                "some-db",
                "some-path",
            ],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.os.makedirs", MagicMock())
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_skip_platform_failure(self, mock_os_path_exists):
        def side_effect(arg):
            if arg == "some-path":
                return False
            return True

        mock_os_path_exists.side_effect = side_effect

        with pytest.raises(ELIPYException):
            elipy2.avalanche.deploy("some-db", "ps5", "some-path", include_platform=False)

    @patch("elipy2.avalanche.os.makedirs")
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_create_dir(self, mock_os_path_exists, mock_makedirs):
        mock_os_path_exists.return_value = False
        elipy2.avalanche.deploy("some-db", "ps5", "some-path")
        mock_makedirs.assert_called_once_with("some-path")

    @patch("elipy2.avalanche.os.makedirs")
    @patch("elipy2.avalanche.os.path.exists")
    def test_deploy_dir_exists(self, mock_os_path_exists, mock_makedirs):
        mock_os_path_exists.return_value = True
        elipy2.avalanche.deploy("some-db", "ps5", "some-path")
        assert mock_makedirs.call_count == 0

    def test_combine(self):
        elipy2.avalanche.combine("input_db_1", "input_db_2", "output_db")
        self.mock_run.assert_called_once_with(
            [
                elipy2.local_paths.get_avalanchecli_exe_path(),
                "combine",
                "output_db",
                "input_db_1",
                "input_db_2",
            ],
            print_std_out=True,
        )

    def test_combine_extra_args(self):
        elipy2.avalanche.combine(
            "input_db_1", "input_db_2", "output_db", extra_combine_args=["-s", "settings.yaml"]
        )
        self.mock_run.assert_called_once_with(
            [
                elipy2.local_paths.get_avalanchecli_exe_path(),
                "combine",
                "output_db",
                "input_db_1",
                "input_db_2",
                "-s",
                "settings.yaml",
            ],
            print_std_out=True,
        )

    def test_get_built_levels(self):
        res = elipy2.avalanche.get_built_levels("BattlefieldGameData.kin-dev.Win32.Debug")
        assert res is not None
        assert len(res) == 39

    def test_get_built_levels_with_open(self):
        mock_open = mock.mock_open()
        with patch("elipy2.avalanche.open", mock_open, create=True):
            res = elipy2.avalanche.get_built_levels(
                "BattlefieldGameData.kin-dev.Win32.Debug", to_file="some-file"
            )
            assert res is not None
            assert len(res) == 39
            mock_open.assert_called_once()

    @patch("os.makedirs")
    @patch("os.path.exists")
    def test_get_built_levels_create_path(self, mock_os_path_exists, mock_makedirs):
        mock_os_path_exists.return_value = False
        mock_open = mock.mock_open()
        with patch("elipy2.avalanche.open", mock_open, create=True):
            elipy2.avalanche.get_built_levels(
                "BattlefieldGameData.kin-dev.Win32.Debug",
                to_file=os.path.join("some-dir", "some-file"),
            )
        mock_makedirs.assert_called_once_with("some-dir")

    @patch("os.makedirs")
    @patch("os.path.exists")
    def test_get_built_levels_path_exists(self, mock_os_path_exists, mock_makedirs):
        mock_os_path_exists.return_value = True
        mock_open = mock.mock_open()
        with patch("elipy2.avalanche.open", mock_open, create=True):
            elipy2.avalanche.get_built_levels(
                "BattlefieldGameData.kin-dev.Win32.Debug",
                to_file=os.path.join("some-dir", "some-file"),
            )
        assert call("some-dir") not in mock_makedirs.mock_calls

    def test_get_ops_chain_with_open(self):
        mock_open = mock.mock_open()
        with patch("elipy2.avalanche.open", mock_open, create=True):
            elipy2.avalanche.get_ops_chain(
                "BattlefieldGameData.kin-dev.Win32.Debug", to_file="some-file"
            )
            mock_open.assert_called_once()

    def test_get_ops_chain_exception(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche.get_ops_chain("some-db", to_file=None)

    def test_get_local_avalanche(self):
        assert elipy2.avalanche.get_local_avalanche() == "http://fakeymcfake-node-01:1338/Avalanche"

    def test_get_avalanche_platform_name(self):
        assert elipy2.avalanche.get_avalanche_platform_name("xb1") == "Gen4a"
        assert elipy2.avalanche.get_avalanche_platform_name("xb1") != "Gen4b"

    def test_get_avalanche_platform_name_reverse(self):
        platform_gen4b = elipy2.avalanche.get_reverse_avalanche_platform("Gen4b")
        platform_gen4a = elipy2.avalanche.get_reverse_avalanche_platform("Gen4a")
        platform_win32 = elipy2.avalanche.get_reverse_avalanche_platform("Win32")
        platform_dedi = elipy2.avalanche.get_reverse_avalanche_platform("DedicatedServer")
        platform_linux = elipy2.avalanche.get_reverse_avalanche_platform("Linux")
        # Tests True
        assert all(elem in ("gen4b", "ps4") for elem in platform_gen4b)
        assert all(elem in ("xb1", "gen4a") for elem in platform_gen4a)
        assert all(elem in ("pc", "win64", "win32", "win64game") for elem in platform_win32)
        assert all(
            elem in ("win64server", "server", "linuxserver", "dedicatedserver")
            for elem in platform_dedi
        )
        assert all(elem in ("linux", "linux64") for elem in platform_linux)
        # Tests False
        assert not all(elem in ("xb1", "gen4a") for elem in platform_gen4b)
        assert not all(elem in ("gen4b", "ps4") for elem in platform_gen4a)
        assert not all(
            elem in ("win64server", "server", "linuxserver", "dedicatedserver")
            for elem in platform_win32
        )
        assert not all(elem in ("pc", "win64", "win32", "win64game") for elem in platform_dedi)
        assert not all(elem in ("xb1", "gen4a") for elem in platform_linux)

    @patch("elipy2.avalanche._http_get")
    def test_avalanche_blocker_check(self, mock_return):
        mock_return.return_value = [
            {
                "id": 13,
                "oid": "6241cef2b7ab717e4cd29ba1",
                "parentOid": "000000000000000000000000",
                "initiatorSession": "000000000000000000000000",
                "description": "Collecting Garbage",
                "state": "running",
                "enqueued": "2022-03-28T15:06:26Z",
                "started": "2022-03-28T15:06:27Z",
                "done": "2022-03-28T15:06:29Z",
                "duration": "2017543",
                "result": {"ok": True},
                "summary": {},
            }
        ]
        with pytest.raises(AvalancheException):
            elipy2.avalanche.avalanche_blocker_check()

    @patch("elipy2.avalanche._http_get")
    def test_avalanche_blocker_check_not_running(self, mock_return):
        mock_return.return_value = [
            {
                "id": 13,
                "description": "Collecting Garbage",
                "state": "done",
            }
        ]
        elipy2.avalanche.avalanche_blocker_check()
        self.mock_get_computer_name.assert_called_once_with()

    @patch("elipy2.avalanche._http_get")
    def test_avalanche_blocker_check_no_relevant_process(self, mock_return):
        mock_return.return_value = [
            {
                "id": 13,
                "description": "Some other process",
                "state": "done",
            }
        ]
        elipy2.avalanche.avalanche_blocker_check()
        self.mock_get_computer_name.assert_called_once_with()

    @patch("elipy2.avalanche._http_get")
    def test_avalanche_blocker_check_multiple(self, mock_return):
        mock_return.return_value = [
            {
                "id": 13,
                "description": "Collecting Garbage",
                "state": "done",
            },
            {
                "id": 14,
                "description": "Collecting Garbage",
                "state": "running",
            },
        ]
        with pytest.raises(AvalancheException):
            elipy2.avalanche.avalanche_blocker_check()

    @patch("elipy2.avalanche.clean_empty_dbs")
    @patch("elipy2.avalanche.clean_old_dbs")
    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status")
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_running(
        self, mock_query, mock_status, mock_clean_dbs, mock_clean_empty_dbs
    ):
        mock_query.return_value = "Running"
        mock_status.return_value = {"extentAvailBytes": 30000000000}
        assert elipy2.avalanche.avalanche_maintenance(drop_old_dbs=True) == 0
        assert mock_clean_dbs.call_count == 2
        assert mock_clean_empty_dbs.call_count == 1

    @patch("elipy2.avalanche.clean_empty_dbs", MagicMock())
    @patch("elipy2.avalanche.clean_old_dbs")
    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status")
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_low_space(self, mock_query, mock_status, mock_clean_old_dbs):
        mock_query.return_value = "Running"
        mock_status.return_value = {"extentAvailBytes": 30000000000}
        assert elipy2.avalanche.avalanche_maintenance(drop_old_dbs=True) == 0
        mock_clean_old_dbs.assert_has_calls(
            [
                call(days_to_store=6),
                call(days_to_store=1),
            ]
        )

    @patch("elipy2.avalanche.clean_empty_dbs", MagicMock())
    @patch("elipy2.avalanche.clean_old_dbs")
    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status")
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_medium_space(self, mock_query, mock_status, mock_clean_old_dbs):
        mock_query.return_value = "Running"
        mock_status.return_value = {"extentAvailBytes": 50000000000}
        assert elipy2.avalanche.avalanche_maintenance(drop_old_dbs=True) == 0
        mock_clean_old_dbs.assert_has_calls(
            [
                call(days_to_store=6),
                call(days_to_store=3),
            ]
        )

    @patch("elipy2.avalanche.clean_empty_dbs", MagicMock())
    @patch("elipy2.avalanche.clean_old_dbs")
    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status")
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_high_space(self, mock_query, mock_status, mock_clean_old_dbs):
        mock_query.return_value = "Running"
        mock_status.return_value = {"extentAvailBytes": 120000000000}
        assert elipy2.avalanche.avalanche_maintenance(drop_old_dbs=True) == 0
        mock_clean_old_dbs.assert_called_once_with(days_to_store=6)

    @patch("elipy2.avalanche.clean_empty_dbs")
    @patch("elipy2.avalanche.clean_old_dbs")
    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status")
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_running_nodb(
        self, mock_query, mock_status, mock_clean_dbs, mock_clean_empty_dbs
    ):
        mock_query.return_value = "Running"
        mock_status.return_value = {"extentAvailBytes": 30000000000}
        assert elipy2.avalanche.avalanche_maintenance(drop_old_dbs=False) == 0
        assert mock_clean_empty_dbs.call_count == 1
        assert mock_clean_dbs.call_count == 0

    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status", MagicMock())
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_not_running(self, mock_query):
        mock_query.return_value = "ohnoes"
        with pytest.raises(AvalancheException):
            elipy2.avalanche.avalanche_maintenance()

    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.avalanche.avalanche_status", MagicMock())
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_maintenance_ret_failed(self, mock_query):
        mock_query.return_value = "ohnoes"
        with pytest.raises(AvalancheException):
            elipy2.avalanche.avalanche_maintenance()

    @patch("elipy2.windows_tools.query_service")
    def test_check_avalanche_service_when_running(self, mock_service_status):
        mock_service_status.return_value = "Running"
        assert elipy2.avalanche.check_avalanche_service()

    @patch("elipy2.windows_tools.query_service")
    def test_check_avalanche_service_when_not_running(self, mock_service_status):
        mock_service_status.return_value = "Unknown"
        assert not elipy2.avalanche.check_avalanche_service()

    def test_check_avalanche_service_api_when_running(self):
        assert elipy2.avalanche.check_avalanche_service_api()

    def test_check_avalanche_service_api_when_not_running(self):
        assert not elipy2.avalanche.check_avalanche_service_api(
            avalanche_url="http://localhost_unavailable:1338/"
        )

    def test_check_avalanche_service_api_when_not_return(self):
        assert not elipy2.avalanche.check_avalanche_service_api(
            avalanche_url="http://localhost_redirect:1338/"
        )

    @patch("elipy2.windows_tools.start_service")
    @patch("elipy2.avalanche.check_avalanche_service")
    def test_restart_avalanche_true(self, mock_service_status, mock_start_service):
        mock_service_status.return_value = True
        self.mock_get_computer_name.return_value = "localhost_unavailable"
        elipy2.avalanche.restart_avalanche("http://localhost_unavailable:1338/")
        mock_start_service.assert_called_once_with("Avalanche")

    @patch("time.sleep")
    @patch("elipy2.windows_tools.query_service")
    @patch("elipy2.windows_tools.start_service", MagicMock())
    @patch("elipy2.avalanche.check_avalanche_service")
    def test_restart_avalanche_timeout(self, mock_service_status, mock_query_service, mock_sleep):
        mock_service_status.return_value = True
        self.mock_get_computer_name.return_value = "localhost_unavailable"
        mock_query_service.side_effect = [
            "Unknown",
            "Unknown",
            "Unknown",
            "Unknown",
            "Unknown",
            "Running",
        ]
        elipy2.avalanche.restart_avalanche("http://localhost_unavailable:1338/")
        assert mock_sleep.call_count == 5

    @patch("elipy2.windows_tools.start_service")
    @patch("elipy2.avalanche.check_avalanche_service")
    @patch("elipy2.avalanche.check_avalanche_service_api")
    def test_restart_avalanche_avalanche_already_running(
        self, mock_check_avalanche_service_api, mock_service_status, mock_start_service
    ):
        mock_check_avalanche_service_api.return_value = True
        mock_service_status.return_value = True
        self.mock_get_computer_name.return_value = "localhost_unavailable"
        elipy2.avalanche.restart_avalanche("http://localhost_unavailable:1338/")
        assert mock_start_service.call_count == 0

    @patch("elipy2.windows_tools.shutdown_service")
    @patch("elipy2.avalanche.check_avalanche_service")
    def test_stop_avalanche_true(self, mock_service_status, mock_shutdown_service):
        mock_service_status.return_value = True
        self.mock_get_computer_name.return_value = "localhost_unavailable"
        elipy2.avalanche.stop_avalanche("http://localhost_unavailable:1338/")
        mock_shutdown_service.assert_called_once_with("Avalanche")

    @patch("elipy2.windows_tools.shutdown_service")
    @patch("elipy2.avalanche.check_avalanche_service")
    def test_stop_avalanche_already_stopped(self, mock_service_status, mock_shutdown_service):
        mock_service_status.return_value = False
        self.mock_get_computer_name.return_value = "localhost_unavailable"
        elipy2.avalanche.stop_avalanche("http://localhost_unavailable:1338/")
        assert mock_shutdown_service.call_count == 0

    @patch("elipy2.avalanche.windows_tools.convert_bytes_to_human_readable")
    @patch("elipy2.avalanche.json.loads")
    @patch("elipy2.avalanche.avalanche_invoke", MagicMock())
    @patch("elipy2.windows_tools.query_service")
    def test_avalanche_status(self, mock_query, mock_load, mock_convert):
        with patch("elipy2.avalanche.six.moves.urllib.request.urlopen") as openurl:
            mock_convert.return_value = 1
            mock_load.return_value = {
                "Primary": {
                    "pools": [
                        {
                            "totalDiskSpace": "12",
                            "freeDiskSpace": "4",
                            "extentCount": "1234",
                            "extentAvailBytes": "1234",
                            "overflowSize": "1234",
                        }
                    ]
                }
            }
            mock_open = MagicMock()
            mock_open.read.return_value = """{u'result': {u'symbols': [
                {u'meta': {u'name': u'sym_name', u'created': u'2018-05-04T11:50:52Z'},
                u'key': u'1234'}]}}"""
            mock_open.status_code = 204
            openurl.return_value = mock_open

            mock_query.return_value = "ohnoes"
            elipy2.avalanche.avalanche_status()

    @patch("elipy2.windows_tools.get_free_disk_space")
    @patch("elipy2.avalanche.get_db_all_data")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.avalanche_status")
    def test_cleanup_temp_dbs_no_cleanup(
        self, mock_avalanche_status, mock_drop, mock_loads, mock_get_free_disk_space
    ):
        mock_loads.return_value = get_mock_all_db_data()[:5]
        mock_avalanche_status.return_value = {"extentAvailBytes": 30000000000}
        mock_get_free_disk_space.return_value = 51

        elipy2.avalanche.cleanup_temp_dbs()
        assert mock_drop.call_count == 0

    @patch("elipy2.windows_tools.get_free_disk_space")
    @patch("elipy2.avalanche.get_db_all_data")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.avalanche_status")
    def test_cleanup_temp_dbs_no_cleanup_too_few_dbs(
        self, mock_avalanche_status, mock_drop, mock_loads, mock_get_free_disk_space
    ):
        mock_loads.return_value = get_mock_all_db_data()[:5]
        mock_avalanche_status.return_value = {"extentAvailBytes": 30000000000}
        mock_get_free_disk_space.return_value = 51

        elipy2.avalanche.cleanup_temp_dbs()
        assert mock_drop.call_count == 0

    @patch("elipy2.windows_tools.get_free_disk_space")
    @patch("elipy2.avalanche.get_db_all_data")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.avalanche_status")
    def test_cleanup_temp_dbs_no_cleanup_too_must_space(
        self, mock_avalanche_status, mock_drop, mock_loads, mock_get_free_disk_space
    ):
        mock_loads.return_value = get_mock_all_db_data()
        mock_avalanche_status.return_value = {"extentAvailBytes": 30000000000}
        mock_get_free_disk_space.return_value = 51

        elipy2.avalanche.cleanup_temp_dbs()
        assert mock_drop.call_count == 0

    @patch("elipy2.windows_tools.get_free_disk_space")
    @patch("elipy2.avalanche.get_db_all_data")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.avalanche_status")
    def test_cleanup_temp_dbs_cleanup_avalanche_space(
        self, mock_avalanche_status, mock_drop, mock_loads, mock_get_free_disk_space
    ):
        mock_loads.return_value = get_mock_all_db_data()
        mock_avalanche_status.return_value = {"extentAvailBytes": 10000000000}
        mock_get_free_disk_space.return_value = 51

        elipy2.avalanche.cleanup_temp_dbs()
        assert mock_drop.call_count == 11

    @patch("elipy2.windows_tools.get_free_disk_space")
    @patch("elipy2.avalanche.get_db_all_data")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.avalanche_status")
    def test_cleanup_temp_dbs_cleanup(
        self, mock_avalanche_status, mock_drop, mock_loads, mock_get_free_disk_space
    ):
        mock_loads.return_value = get_mock_all_db_data()
        mock_avalanche_status.return_value = {"extentAvailBytes": 30000000000}
        mock_get_free_disk_space.return_value = 49

        elipy2.avalanche.cleanup_temp_dbs()
        assert mock_drop.call_count == 11

    @patch("elipy2.avalanche.six.moves.urllib.request.urlopen")
    def test_get_db_all_data(self, mock_urlopen):
        mock_urlopen.return_value = MockResponse()
        elipy2.avalanche.get_db_all_data("some_server_url")
        assert mock_urlopen.call_count == 1
        assert mock_urlopen.return_value.read_count == 1

    @patch("elipy2.avalanche.six.moves.urllib.request.urlopen")
    @patch("elipy2.avalanche.six.moves.urllib.request.Request", MagicMock())
    def test_avalanche_invoke(self, mock_urlopen):
        mock_urlopen.return_value = MockResponse()
        elipy2.avalanche.avalanche_invoke("fakeurl", "fakepost")

    @patch("elipy2.avalanche.six.moves.urllib.request.urlopen", MagicMock())
    @patch("elipy2.avalanche.json.load")
    def test_check_avalanche_space(self, mock_load):
        mock_load.return_value = {
            "spillExtent": {"totalPayload": -2},
            "available": 5,
            "capacity": 5,
        }
        elipy2.avalanche.check_avalanche_space()

    @patch("elipy2.avalanche.six.moves.urllib.request.urlopen", MagicMock())
    @patch("elipy2.avalanche.json.load")
    def test_check_avalanche_space_failure(self, mock_load):
        mock_load.return_value = {
            "spillExtent": {"totalPayload": -2},
            "available": 1,
            "capacity": 10,
        }
        with pytest.raises(WindowsServiceException):
            elipy2.avalanche.check_avalanche_space()

    @patch("elipy2.avalanche.six.moves.urllib.request.urlopen", MagicMock())
    @patch("elipy2.avalanche.json.load")
    def test_check_avalanche_space_overflow(self, mock_load):
        mock_load.return_value = {
            "spillExtent": {"totalPayload": 1},
            "available": 5,
            "capacity": 5,
        }
        with pytest.raises(AvalancheException):
            elipy2.avalanche.check_avalanche_space()

    @patch("elipy2.avalanche.get_database_id")
    @patch("elipy2.avalanche.get_fb_branch_id")
    def test_get_avalanche_db(self, mock_fbid, mock_id):
        mock_fbid.return_value = "branch"
        mock_id.return_value = "proj"
        assert elipy2.avalanche.get_avalanche_db("win64") == "proj.branch.Win32"

    @patch("elipy2.avalanche.get_database_id")
    @patch("elipy2.avalanche.get_fb_branch_id")
    def test_get_avalanche_db_valid(self, mock_fbid, mock_id):
        mock_fbid.return_value = "branch"
        mock_id.return_value = "proj"
        assert elipy2.avalanche.get_avalanche_db_valid("win64") == "proj.branch.win64"

    @patch("elipy2.avalanche.get_database_id")
    @patch("elipy2.avalanche.get_fb_branch_id")
    def test_get_avalanche_db_default(self, mock_fbid, mock_id):
        mock_fbid.return_value = "default"
        mock_id.return_value = "proj"
        assert elipy2.avalanche.get_avalanche_db("win64") == "proj.Win32"

    @patch("elipy2.avalanche.get_database_id")
    @patch("elipy2.avalanche.get_fb_branch_id")
    def test_get_avalanche_db_valid_default(self, mock_fbid, mock_id):
        mock_fbid.return_value = "default"
        mock_id.return_value = "proj"
        assert elipy2.avalanche.get_avalanche_db_valid("win64") == "proj.win64"

    def test_get_temp_db_name(self):
        platform, data_changelist, code_changelist = "ps4", "123456", "987654"
        temp_db_name = elipy2.avalanche.get_temp_db_name(platform, data_changelist, code_changelist)
        assert temp_db_name is not None
        assert isinstance(temp_db_name, basestring)
        assert temp_db_name == "temp_{}_{}_{}".format(platform, data_changelist, code_changelist)

    def test_get_temp_db_name_custom_prefix(self):
        platform, data_changelist, code_changelist, prefix, data_branch = (
            "ps4",
            "17755696",
            "17755696",
            "FBNullLicensee",
            "dev-na",
        )

        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, data_branch, prefix
        )

        assert temp_db_name is not None
        assert isinstance(temp_db_name, basestring)
        assert temp_db_name == "{}_{}_{}_{}_{}".format(
            prefix, platform, data_changelist, code_changelist, data_branch
        )

    def test_get_autotest_temp_db_name(self):
        platform, category, data_changelist, code_changelist = (
            "ps4",
            "test_category",
            "123456",
            "987654",
        )

        temp_db_name = elipy2.avalanche.get_autotest_temp_db_name(
            platform, category, data_changelist, code_changelist
        )

        assert temp_db_name is not None
        assert isinstance(temp_db_name, basestring)
        assert temp_db_name == "temp_{}_{}_{}.autotest.{}".format(
            platform, data_changelist, code_changelist, category
        )

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2019-PR7"))
    def test_get_temp_db_name_2019_pr7(self):
        platform, data_changelist, code_changelist = "ps4", "123456", "987654"
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, "branch"
        )

        assert temp_db_name is not None
        assert isinstance(temp_db_name, basestring)
        assert temp_db_name == "temp_db.{}_{}.{}_branch".format(
            data_changelist, code_changelist, platform
        )

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2018.0"))
    def test_get_temp_db_name_2018(self):
        platform, data_changelist, code_changelist = "ps4", "123456", "987654"
        temp_db_name = elipy2.avalanche.get_temp_db_name(platform, data_changelist, code_changelist)
        assert temp_db_name is not None
        assert isinstance(temp_db_name, basestring)
        assert temp_db_name == "temp_{}_{}_{}".format(platform, data_changelist, code_changelist)

    def test_get_db_name(self):
        platform, data_changelist, code_changelist = "ps4", "123456", "987654"
        db_name = elipy2.avalanche.get_db_name(platform, data_changelist, code_changelist)
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "db{}_{}_{}".format(platform, data_changelist, code_changelist)

    def test_get_db_name_prefix(self):
        platform, data_changelist, code_changelist, prefix = "ps4", "123456", "987654", "prefix_"
        db_name = elipy2.avalanche.get_db_name(
            platform, data_changelist, code_changelist, prefix=prefix
        )
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "{}{}_{}_{}".format(prefix, platform, data_changelist, code_changelist)

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2019-PR7"))
    def test_get_db_name_fb2019_pr7(self):
        platform, data_changelist, code_changelist = "ps4", "123456", "987654"
        db_name = elipy2.avalanche.get_db_name(platform, data_changelist, code_changelist)
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "db.{}_{}.{}".format(data_changelist, code_changelist, platform)

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2019-PR7"))
    def test_get_db_name_prefix_fb2019_pr7(self):
        platform, data_changelist, code_changelist, prefix = "ps4", "123456", "987654", "prefix_"
        db_name = elipy2.avalanche.get_db_name(
            platform, data_changelist, code_changelist, prefix=prefix
        )
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "{}.{}_{}.{}".format(prefix, data_changelist, code_changelist, platform)

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2019-PR7"))
    def test_get_db_name_fb2019_pr7_server(self):
        platform, data_changelist, code_changelist = "server", "123456", "987654"
        db_name = elipy2.avalanche.get_db_name(platform, data_changelist, code_changelist)
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "db.{}_{}.{}".format(data_changelist, code_changelist, "dedicatedserver")

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2018.0"))
    def test_get_db_name_fb2018(self):
        platform, data_changelist, code_changelist = "ps4", "123456", "987654"
        db_name = elipy2.avalanche.get_db_name(platform, data_changelist, code_changelist)
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "db{}_{}_{}".format(platform, data_changelist, code_changelist)

    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2018.0"))
    def test_get_db_name_prefix_fb2018(self):
        platform, data_changelist, code_changelist, prefix = "ps4", "123456", "987654", "prefix_"
        db_name = elipy2.avalanche.get_db_name(
            platform, data_changelist, code_changelist, prefix=prefix
        )
        assert db_name is not None
        assert isinstance(db_name, basestring)
        assert db_name == "{}{}_{}_{}".format(prefix, platform, data_changelist, code_changelist)

    @patch("elipy2.core.delete_folder")
    def test_export_avalanche_state_exception(self, mock_core_delete_folder):
        elipy2.avalanche.clean_temp_state_folder("testpath")
        mock_core_delete_folder.assert_called_once_with("testpath")
        mock_core_delete_folder.side_effect = [Exception(), mock.DEFAULT]
        elipy2.avalanche.clean_temp_state_folder("testpath")
        assert mock_core_delete_folder.call_count == 3

    @patch("elipy2.data.DataUtils.cook")
    @patch("elipy2.filer.FilerUtils.deploy_avalanche_state")
    @patch("elipy2.avalanche.export")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    @patch(
        "elipy2.build_metadata_utils.setup_metadata_manager",
        MagicMock(return_value=MagicMock(spec=build_metadata.BuildMetadataManager)),
    )
    @patch("elipy2.core.robocopy", MagicMock())
    def test_export_avalanche_state(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_avalanche_export,
        mock_filer_filerutils,
        mock_cook,
    ):
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"

        mock_filer_filerutils.return_value = "filer\\path"
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, branch
        )
        local_path = os.path.join(
            elipy2.frostbite_core.get_tnt_root(),
            "Local",
            "import.{0}.{1}.{2}.{3}".format(branch, platform, data_changelist, code_changelist),
        )

        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        elipy2.avalanche.export_avalanche_state(
            builder, "game-dev", "12345", "54321", elipy2.filer.FilerUtils()
        )

        mock_cook.assert_called_once_with(pipeline_args=["-exportState", temp_db_name])
        mock_avalanche_export.assert_called_once_with(temp_db_name, local_path)
        mock_avalanche_drop.assert_called_once_with(temp_db_name)
        mock_core_delete_folder.assert_called_once_with(local_path)

    @patch("elipy2.data.DataUtils.cook")
    @patch("elipy2.filer.FilerUtils.deploy_avalanche_state")
    @patch("elipy2.avalanche.export")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    @patch(
        "elipy2.build_metadata_utils.setup_metadata_manager",
        MagicMock(return_value=MagicMock(spec=build_metadata.BuildMetadataManager)),
    )
    @patch("elipy2.core.robocopy", MagicMock())
    @patch("elipy2.frostbite_core.minimum_fb_version", MagicMock(return_value=False))
    def test_export_avalanche_state_old_fb_version(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_avalanche_export,
        mock_filer_deploy_avalanche_state,
        mock_cook,
    ):
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"

        mock_filer_deploy_avalanche_state.return_value = "filer\\path"
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, branch
        )
        local_path = os.path.join(
            elipy2.frostbite_core.get_tnt_root(),
            "Local",
            "import.{0}.{1}.{2}.{3}".format(branch, platform, data_changelist, code_changelist),
        )

        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        elipy2.avalanche.export_avalanche_state(
            builder, "game-dev", "12345", "54321", elipy2.filer.FilerUtils()
        )

        mock_cook.assert_called_once_with(pipeline_args=["-exportState", temp_db_name])
        mock_avalanche_export.assert_called_once_with(temp_db_name, local_path)
        mock_avalanche_drop.assert_called_once_with(temp_db_name)
        mock_core_delete_folder.assert_called_once_with(local_path)

    @patch("elipy2.avalanche.remote_clone_db")
    @patch("elipy2.data.DataUtils.cook", MagicMock())
    @patch("elipy2.avalanche.export", MagicMock())
    @patch("elipy2.avalanche.drop", MagicMock())
    @patch("elipy2.avalanche.clean_temp_state_folder", MagicMock())
    @patch(
        "elipy2.build_metadata_utils.setup_metadata_manager",
        MagicMock(return_value=MagicMock(spec=build_metadata.BuildMetadataManager)),
    )
    @patch("elipy2.core.robocopy", MagicMock())
    def test_export_avalanche_state_remote_clone_db(self, mock_remote_clone_db):
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, branch
        )
        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        elipy2.avalanche.export_avalanche_state(builder, "game-dev", "12345", "54321")
        mock_remote_clone_db.assert_called_once_with(
            source_db=temp_db_name,
            dest_db=temp_db_name,
            dest_host="test-avalanche.dice.ad.ea.com",
            source_host="fakeymcfake-node-01",
            push_built_levels=False,
            complete_clone=True,
        )

    @patch("elipy2.data.DataUtils.cook", MagicMock())
    @patch("elipy2.filer.FilerUtils.deploy_state")
    @patch("elipy2.filer.FilerUtils.deploy_avalanche_state", MagicMock(return_value="filer\\path"))
    @patch("elipy2.avalanche.export", MagicMock())
    @patch("elipy2.avalanche.drop", MagicMock())
    @patch("elipy2.avalanche.clean_temp_state_folder", MagicMock())
    @patch(
        "elipy2.build_metadata_utils.setup_metadata_manager",
        MagicMock(return_value=MagicMock(spec=build_metadata.BuildMetadataManager)),
    )
    @patch("elipy2.core.robocopy", MagicMock())
    def test_export_avalanche_state_deploy_state_to_bundles(self, mock_filer_deploy_state):
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"
        local_path = os.path.join(
            elipy2.frostbite_core.get_tnt_root(),
            "Local",
            "import.{0}.{1}.{2}.{3}".format(branch, platform, data_changelist, code_changelist),
        )
        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        elipy2.avalanche.export_avalanche_state(
            builder,
            "game-dev",
            "12345",
            "54321",
            elipy2.filer.FilerUtils(),
            deploy_state_to_bundles=True,
        )
        mock_filer_deploy_state.assert_called_once_with(
            local_path,
            data_branch="game-dev",
            data_changelist="12345",
            code_branch="game-dev",
            code_changelist="54321",
            platform="win64",
        )

    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("os.path.exists")
    @patch("elipy2.filer.FilerUtils.fetch_avalanche_state")
    @patch("elipy2.avalanche.importdb")
    @patch("elipy2.core.robocopy", MagicMock())
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    def test_import_avalanche_state_success(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_avalanche_importdb,
        mock_filer_filerutils,
        mock_os_path_exists,
    ):
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"
        mock_os_path_exists.return_value = True
        mock_filer_filerutils.return_value = True
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, branch
        )
        local_path = os.path.join(
            elipy2.frostbite_core.get_tnt_root(),
            "Local",
            "import.{0}.{1}.{2}.{3}".format(branch, platform, data_changelist, code_changelist),
        )

        assert elipy2.avalanche.import_avalanche_state(
            "win64",
            "game-dev",
            "12345",
            "54321",
            elipy2.filer.FilerUtils(),
            avalanche_url="http://localhost_import_state:1338/",
        ) == ["-importState", temp_db_name]

        mock_avalanche_importdb.assert_called_once_with(local_path, temp_db_name)
        source = os.path.join(
            "\\\\filer.test\\builds\\DICE", "AvalancheState", "game-dev", "win64", "12345", "54321"
        )
        mock_avalanche_drop.assert_called()
        mock_core_delete_folder.assert_called_once_with(local_path)

    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("os.path.exists")
    @patch("elipy2.filer.FilerUtils.fetch_avalanche_state")
    @patch("elipy2.avalanche.importdb")
    @patch("elipy2.core.robocopy", MagicMock())
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    def test_import_avalanche_state_success_with_data(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_avalanche_importdb,
        mock_filer_filerutils,
        mock_os_path_exists,
    ):
        # temp.game-dev.win64.12345.54321
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"
        mock_os_path_exists.return_value = True
        mock_filer_filerutils.return_value = True
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, branch
        )
        local_path = os.path.join(
            elipy2.frostbite_core.get_tnt_root(),
            "Local",
            "import.{0}.{1}.{2}.{3}".format(branch, platform, data_changelist, code_changelist),
        )

        assert elipy2.avalanche.import_avalanche_state(
            "win64",
            "game-dev",
            "12345",
            "54321",
            elipy2.filer.FilerUtils(),
            _data=elipy2.data.DataUtils("win64", []),
            avalanche_url="http://localhost_import_state:1338/",
        ) == ["-importState", temp_db_name]

        mock_avalanche_importdb.assert_called_once_with(local_path, temp_db_name)
        source = os.path.join(
            "\\\\filer.test\\builds\\DICE", "AvalancheState", "game-dev", "win64", "12345", "54321"
        )
        mock_avalanche_drop.assert_called()
        mock_core_delete_folder.assert_called_once_with(local_path)
        assert self.mock_index.called

    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("os.path.exists")
    @patch("elipy2.frostbite.fbenv_layer.cook")
    @patch("elipy2.filer.FilerUtils.fetch_avalanche_state")
    @patch("elipy2.avalanche.importdb")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    def test_import_avalanche_state_already_imported(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_core_robocopy,
        mock_avalanche_importdb,
        mock_filer_filerutils,
        mock_cook,
        mock_os_path_exists,
    ):
        mock_os_path_exists.return_value = False
        mock_filer_filerutils.side_effect = Exception()

        assert (
            elipy2.avalanche.import_avalanche_state(
                "win64", "game-dev", "12345", "54321", elipy2.filer.FilerUtils()
            )
            == []
        )

        assert not mock_cook.called
        assert not mock_avalanche_importdb.called
        assert not mock_core_robocopy.called
        assert not mock_avalanche_drop.called
        assert not mock_core_delete_folder.called

    @patch("elipy2.avalanche.db_contains", MagicMock(return_value=True))
    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.frostbite.fbenv_layer.cook")
    @patch("elipy2.filer.FilerUtils.fetch_avalanche_state")
    @patch("elipy2.avalanche.importdb")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    def test_import_avalanche_state_skip_no_new_code(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_core_robocopy,
        mock_avalanche_importdb,
        mock_filer_filerutils,
        mock_cook,
    ):
        mock_filer_filerutils.side_effect = Exception()

        assert (
            elipy2.avalanche.import_avalanche_state(
                "win64", "game-dev", "12345", "54321", elipy2.filer.FilerUtils(), only_new_code=True
            )
            == []
        )

        assert not mock_cook.called
        assert not mock_avalanche_importdb.called
        assert not mock_core_robocopy.called
        assert not mock_avalanche_drop.called
        assert not mock_core_delete_folder.called

    @patch("elipy2.avalanche.reimport_needed", MagicMock(return_value=False))
    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.frostbite.fbenv_layer.cook")
    @patch("elipy2.filer.FilerUtils.fetch_avalanche_state")
    @patch("elipy2.avalanche.importdb")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    def test_import_avalanche_state_skip_reimport_not_needed(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_core_robocopy,
        mock_avalanche_importdb,
        mock_filer_filerutils,
        mock_cook,
    ):
        mock_filer_filerutils.side_effect = Exception()
        assert (
            elipy2.avalanche.import_avalanche_state(
                "win64",
                "game-dev",
                "12345",
                "54321",
                elipy2.filer.FilerUtils(),
                current_data_changelist="12445",
            )
            == []
        )
        assert not mock_cook.called
        assert not mock_avalanche_importdb.called
        assert not mock_core_robocopy.called
        assert not mock_avalanche_drop.called
        assert not mock_core_delete_folder.called

    @patch("elipy2.avalanche.db_exists", MagicMock(return_value=True))
    @patch("elipy2.avalanche.clean_old_dbs", MagicMock())
    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.frostbite.fbenv_layer.cook")
    @patch("elipy2.filer.FilerUtils.fetch_avalanche_state")
    @patch("elipy2.avalanche.importdb")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.avalanche.drop")
    @patch("elipy2.avalanche.clean_temp_state_folder")
    def test_import_avalanche_state_skip_db_exists(
        self,
        mock_core_delete_folder,
        mock_avalanche_drop,
        mock_core_robocopy,
        mock_avalanche_importdb,
        mock_filer_filerutils,
        mock_cook,
    ):
        mock_filer_filerutils.side_effect = Exception()
        assert (
            elipy2.avalanche.import_avalanche_state(
                "win64", "game-dev", "12345", "54321", elipy2.filer.FilerUtils()
            )
            == []
        )
        assert not mock_cook.called
        assert not mock_avalanche_importdb.called
        assert not mock_core_robocopy.called
        assert not mock_avalanche_drop.called
        assert not mock_core_delete_folder.called

    @patch("elipy2.avalanche.drop")
    def test_clean_old_dbs(self, mock_drop: MagicMock):
        elipy2.avalanche.clean_old_dbs()
        assert mock_drop.call_count == 2

    @patch("elipy2.avalanche.drop")
    def test_clean_old_dbs_long_storage_time(self, mock_drop: MagicMock):
        elipy2.avalanche.clean_old_dbs(days_to_store=99999)
        assert mock_drop.call_count == 0

    def test_db_exists(self):
        assert elipy2.avalanche.db_exists("BattlefieldGameData.kin-dev.Win32.Debug")
        assert not elipy2.avalanche.db_exists("BattlefieldGameData.kin-dev.Win32.Release")

    def test_db_contains(self):
        assert elipy2.avalanche.db_contains(["BattlefieldGameData", "kin", "Debug"])
        assert not elipy2.avalanche.db_contains(["BattlefieldGameData", "kin", "release"])
        assert not elipy2.avalanche.db_contains(["BattlefieldGameData", "kin", "retail"])

    def test__http_put(self):
        elipy2.avalanche._http_put("http://test-url/cache/testdata", '{"data": "value"}')

    def test__http_put_bad_status(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche._http_put("http://test-url/cache/bad_status", '{"data": "value"}')

    @patch("requests.put")
    def test__http_put_default_header(self, mock_requests_put):
        mock_requests_put.return_value = MagicMock()
        mock_requests_put.return_value.status_code = 200
        elipy2.avalanche._http_put("http://test-url/cache/testdata", {"data": "value"})
        mock_requests_put.assert_called_once_with(
            "http://test-url/cache/testdata",
            data={"data": "value"},
            headers={"content-type": "application/json"},
        )

    @patch("requests.put")
    def test__http_put_set_header(self, mock_requests_put):
        mock_requests_put.return_value = MagicMock()
        mock_requests_put.return_value.status_code = 200
        elipy2.avalanche._http_put(
            "http://test-url/cache/testdata", {"data": "value"}, header={"some": "header"}
        )
        mock_requests_put.assert_called_once_with(
            "http://test-url/cache/testdata", data={"data": "value"}, headers={"some": "header"}
        )

    def test_set_cache_value(self):
        elipy2.avalanche.set_cache_value("testdata", "data", avalanche_url="http://test-url")

    def test_get_cache_value(self):
        assert elipy2.avalanche.get_cache_value("key") == {"json": "data"}

    def test_set_cache_value_list(self):
        elipy2.avalanche.set_cache_value(["key1", "key2"], "value")

    def test_get_cache_value_list(self):
        assert elipy2.avalanche.get_cache_value(["key1", "key2"]) == {"json": "data"}

    @patch("elipy2.avalanche.get_cache_value")
    @patch("elipy2.avalanche.db_exists")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_reimport_needed_exception(
        self, mock_get_db_name, mock_db_exists, mock_get_cache_value
    ):
        mock_get_db_name.return_value = "BattlefieldGameData.kin-dev.Win32.Debug"
        mock_db_exists.return_value = False
        mock_get_cache_value.side_effect = Exception()
        assert elipy2.avalanche.reimport_needed("plt", "d_branch", "600")

    @patch("elipy2.avalanche._http_delete")
    def test_drop_build_record(self, mock__http_delete):
        mock__http_delete.return_value = {"deleted": "some_deletion_response"}
        assert elipy2.avalanche.drop_build_record("some_record") == "some_deletion_response"
        mock__http_delete.assert_called_once_with("http://localhost:1338/db/some_record")

    @patch("elipy2.avalanche_web_api.database.Database")
    def test_clean_empty_dbs(self, mock_database):
        db_object = mock_database.return_value
        elipy2.avalanche.clean_empty_dbs(db_object)
        db_object.delete_empty_databases.assert_called_once_with()

    @patch("elipy2.avalanche.drop")
    @patch("json.loads")
    @patch("six.moves.urllib.request.urlopen")
    def test_drop_all_dbs(self, mock_urlopen, mock_json_loads, mock_drop):
        mock_urlopen.return_value = MagicMock()
        mock_json_loads.return_value = [
            {"id": "id_1", "data": "data_1"},
            {"id": "id_2", "data": "data_2"},
        ]
        elipy2.avalanche.drop_all_dbs()
        mock_drop.assert_has_calls(
            [
                call("id_1"),
                call("id_2"),
            ]
        )

    @patch("os.environ.get")
    def test_get_full_database_name(self, mock_os_environ_get):
        mock_os_environ_get.return_value = "db_id.branch_id.{}"
        assert elipy2.avalanche.get_full_database_name("ps5") == "db_id.branch_id.Ps5"

    @patch("os.environ.get")
    def test_get_short_database_name(self, mock_os_environ_get):
        mock_os_environ_get.return_value = "db_id.branch_id"
        assert elipy2.avalanche.get_short_database_name() == "db_id.branch_id"

    @patch(
        "builtins.open", new_callable=mock_open, read_data='Initial line\n<Database id="db_id"/>'
    )
    def test_get_database_id(self, mock_open):
        assert elipy2.avalanche.get_database_id() == "db_id"
        mock_open.assert_called_once_with(
            os.path.join("game_data_dir", "database.dbmanifest"),
        )

    @patch("builtins.open", new_callable=mock_open, read_data="Initial line\nLine without db")
    def test_get_database_id_not_found(self, mock_open):
        assert elipy2.avalanche.get_database_id() == 0
        mock_open.assert_called_once_with(
            os.path.join("game_data_dir", "database.dbmanifest"),
        )

    @patch("os.environ.get")
    def test_get_fb_branch_id(self, mock_os_environ_get):
        mock_os_environ_get.return_value = "fb_branch_id"
        assert elipy2.avalanche.get_fb_branch_id() == "fb_branch_id"

    @patch("os.environ.get")
    def test_get_fb_branch_id_not_found(self, mock_os_environ_get):
        mock_os_environ_get.return_value = None
        with pytest.raises(ELIPYException):
            assert elipy2.avalanche.get_fb_branch_id()


@pytest.mark.usefixtures("mock_avalanche")
@pytest.mark.usefixtures(patch_temp_dir.__name__)
class TestAvalancheCustomComputerName(object):
    def setup(self):
        self.patcher_run = patch("elipy2.core.run")
        self.mock_run = self.patcher_run.start()
        self.mock_run.return_value = (0, [], [])

        self.patcher_mkdirs = patch("os.makedirs")
        self.mock_mkdirs = self.patcher_mkdirs.start()
        self.mock_mkdirs.return_value = True

        self.patcher_get_computer_name = patch("elipy2.windows_tools.get_computer_name")
        self.mock_get_computer_name = self.patcher_get_computer_name.start()
        self.mock_get_computer_name.return_value = "fakeymcfake-node-01"

        self.patcher_ensure_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_config = self.patcher_ensure_config.start()

        self.patcher_process_kill = patch("elipy2.running_processes.kill")
        self.mock_process_kill = self.patcher_process_kill.start()

        self.patcher_query_service = patch("elipy2.windows_tools.query_service")
        self.mock_query_service = self.patcher_query_service.start()

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

        self.patcher_index = patch("elipy2.data.DataUtils.run_indexing")
        self.mock_index = self.patcher_index.start()

    def teardown(self):
        patch.stopall()

    def test_set_avalanche_build_status(self):
        self.mock_get_computer_name.return_value = "test-url"
        elipy2.avalanche.set_avalanche_build_status("c_cl", "d_cl", "d_branch")

    @patch("elipy2.avalanche.get_full_database_name")
    def test_reimport_needed(self, mock_get_db_name):
        self.mock_get_computer_name.return_value = "test-url-reimport"
        mock_get_db_name.return_value = "BattlefieldGameData.kin-dev.Win32.Debug"
        assert not elipy2.avalanche.reimport_needed("plt", "d_branch", "600")
        assert elipy2.avalanche.reimport_needed("plt", "d_branch", "1100")

    @patch("elipy2.avalanche.get_full_database_name", MagicMock())
    @patch("elipy2.avalanche.db_exists")
    def test_reimport_needed_db_doesnt_exist(self, mock_db_exists):
        self.mock_get_computer_name.return_value = "test-url-reimport"
        mock_db_exists.return_value = False
        assert elipy2.avalanche.reimport_needed("plt", "d_branch", "600")

    @patch("elipy2.avalanche.get_cache_value")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_reimport_needed_no_data_changelist(self, mock_get_db_name, mock_get_cache_value):
        self.mock_get_computer_name.return_value = "test-url-reimport"
        mock_get_db_name.return_value = "BattlefieldGameData.kin-dev.Win32.Debug"
        mock_get_cache_value.return_value = {"missing": "data_cl"}
        assert elipy2.avalanche.reimport_needed("plt", "d_branch", "600")

    @patch("elipy2.frostbite_core.minimum_fb_version")
    @patch("elipy2.local_paths.get_avalanchecli_exe_path", MagicMock(return_value="avalanche_path"))
    def test_remote_clone_db(self, mock_minimum_fb_version):
        self.mock_get_computer_name.return_value = "name"
        mock_minimum_fb_version.return_value = False
        elipy2.avalanche.remote_clone_db("db1", "db2", "dest")
        self.mock_run.assert_called_once_with(
            ["avalanche_path", "remoteclone", "db1@name:1338", "db2@dest"],
            print_std_out=True,
        )


class TestAvalancheRegistryKeyUpdate(object):
    def setup(self):
        # Patch winreg in order to run tests in linux
        self.mock_winreg = patch_winreg().start()

    def teardown(self):
        self.mock_winreg.stop()

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_disable_maintenance(self):
        elipy2.avalanche.disable_maintenance()

        self.mock_winreg.OpenKey.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "Software\\WOW6432Node\\Frostbite\\Avalanche\\Avalanche",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )
        self.mock_winreg.SetValueEx.assert_has_calls(
            [
                call(
                    self.mock_winreg.OpenKey().__enter__(),
                    "MaintenanceTimeOfDay",
                    0,
                    self.mock_winreg.REG_DWORD,
                    9999,
                ),
                call(
                    self.mock_winreg.OpenKey().__enter__(),
                    "MaintenanceWindowMinutes",
                    0,
                    self.mock_winreg.REG_DWORD,
                    90,
                ),
            ],
            any_order=True,
        )
        assert self.mock_winreg.SetValueEx.call_count == 2

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_disable_maintenance_file_not_found(self):
        self.mock_winreg.OpenKey.side_effect = FileNotFoundError()
        elipy2.avalanche.disable_maintenance()
        self.mock_winreg.CreateKeyEx.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "Software\\WOW6432Node\\Frostbite\\Avalanche\\Avalanche",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )

    @mock.patch("platform.system", MagicMock(return_value="Linux"))
    def test_disable_maintenance_non_windows(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche.disable_maintenance()

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_disable_upstream_sets(self):
        elipy2.avalanche.disable_upstream_sets()

        self.mock_winreg.OpenKey.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\CacheServer",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )
        self.mock_winreg.SetValueEx.assert_called_once_with(
            self.mock_winreg.OpenKey().__enter__(),
            "PropagateSets",
            0,
            self.mock_winreg.REG_DWORD,
            0,
        )

    @mock.patch("platform.system", MagicMock(return_value="Linux"))
    def test_disable_upstream_sets_non_windows(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche.disable_upstream_sets()

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_set_upstream_node(self):
        elipy2.avalanche.set_upstream_node("test_node")

        self.mock_winreg.OpenKey.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\CacheServer",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )
        self.mock_winreg.SetValueEx.assert_called_once_with(
            self.mock_winreg.OpenKey().__enter__(),
            "UpstreamNodes",
            0,
            self.mock_winreg.REG_MULTI_SZ,
            ["test_node"],
        )

    @mock.patch("platform.system", MagicMock(return_value="Linux"))
    def test_set_upstream_node_non_windows(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche.set_upstream_node("test_node")

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_update_storage_settings(self):
        elipy2.avalanche.update_storage_settings(path="test_path", size=100)

        self.mock_winreg.OpenKey.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\Storage\\StoragePools\\0",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )
        self.mock_winreg.SetValueEx.assert_has_calls(
            [
                call(
                    self.mock_winreg.OpenKey().__enter__(),
                    "Path",
                    0,
                    self.mock_winreg.REG_SZ,
                    "test_path",
                ),
                call(
                    self.mock_winreg.OpenKey().__enter__(),
                    "CapacityGB",
                    0,
                    self.mock_winreg.REG_DWORD,
                    100,
                ),
            ],
            any_order=True,
        )
        assert self.mock_winreg.SetValueEx.call_count == 2

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_update_storage_settings_skip_path(self):
        elipy2.avalanche.update_storage_settings(size=100)

        self.mock_winreg.OpenKey.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\Storage\\StoragePools\\0",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )
        self.mock_winreg.SetValueEx.assert_called_once_with(
            self.mock_winreg.OpenKey().__enter__(),
            "CapacityGB",
            0,
            self.mock_winreg.REG_DWORD,
            100,
        )

    @mock.patch("platform.system", MagicMock(return_value="Windows"))
    def test_update_storage_settings_skip_size(self):
        elipy2.avalanche.update_storage_settings(path="test_path")

        self.mock_winreg.OpenKey.assert_called_once_with(
            self.mock_winreg.HKEY_LOCAL_MACHINE,
            "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\Storage\\StoragePools\\0",
            0,
            self.mock_winreg.KEY_WRITE | self.mock_winreg.KEY_WOW64_64KEY,
        )
        self.mock_winreg.SetValueEx.assert_called_once_with(
            self.mock_winreg.OpenKey().__enter__(),
            "Path",
            0,
            self.mock_winreg.REG_SZ,
            "test_path",
        )

    @mock.patch("platform.system", MagicMock(return_value="Linux"))
    def test_update_storage_settings_non_windows(self):
        with pytest.raises(ELIPYException):
            elipy2.avalanche.update_storage_settings("test_path", 100)


elipy_test_avalanche_config_alt_location = elipy2.config.ConfigManager(
    path=os.path.join(os.path.dirname(__file__), "data", "elipy_test_avalanche.yml"),
    default_location="some_location",
)


@patch("elipy2.avalanche.SETTINGS", elipy_test_avalanche_config_alt_location)
@pytest.mark.usefixtures(patch_temp_dir.__name__)
@pytest.mark.usefixtures("mock_avalanche")
@patch("elipy2.windows_tools.get_computer_name", MagicMock(return_value="fakeymcfake-node-01"))
@patch("elipy2.local_paths.get_avalanchecli_exe_path", MagicMock(return_value="avalanche_path"))
class TestAvalancheAltLocation(object):
    def setup(self):
        self.patcher_run = patch("elipy2.core.run")
        self.mock_run = self.patcher_run.start()
        self.mock_run.return_value = (0, [], [])

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

    def teardown(self):
        patch.stopall()

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_remote_clone_db_ephemeral_no_setting(self, mock_minimum_fb_version):
        mock_minimum_fb_version.return_value = True
        elipy2.avalanche.remote_clone_db(
            "db1", "db2", "dest", "source", branch="some_branch", limited_lifetime=True
        )
        self.mock_run.assert_called_once_with(
            [
                "avalanche_path",
                "remoteclone",
                "db1@source",
                "db2@dest",
                "--ephemeralLifetime",
                str(5 * 24),
            ],
            print_std_out=True,
        )

    @patch("elipy2.avalanche.remote_clone_db")
    @patch("elipy2.data.DataUtils.cook", MagicMock())
    @patch("elipy2.avalanche.export", MagicMock())
    @patch("elipy2.avalanche.drop", MagicMock())
    @patch("elipy2.avalanche.clean_temp_state_folder", MagicMock())
    @patch(
        "elipy2.build_metadata_utils.setup_metadata_manager",
        MagicMock(return_value=MagicMock(spec=build_metadata.BuildMetadataManager)),
    )
    @patch("elipy2.core.robocopy", MagicMock())
    def test_export_avalanche_state_remote_clone_db_no_setting(self, mock_remote_clone_db):
        branch, data_changelist, code_changelist, platform = "game-dev", "12345", "54321", "win64"
        temp_db_name = elipy2.avalanche.get_temp_db_name(
            platform, data_changelist, code_changelist, branch
        )
        builder = elipy2.data.DataUtils("win64", ["retaillevels"])
        elipy2.avalanche.export_avalanche_state(builder, "game-dev", "12345", "54321")
        assert mock_remote_clone_db.call_count == 0
