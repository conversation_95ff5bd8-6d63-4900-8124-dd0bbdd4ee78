<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_folder=stage
code_branch=trunk-to-dev-na
data_folder=stage
data_branch=trunk-to-dev-na
job_label_poolbuild=poolbuild_trunk-to-dev-na
job_label_statebuild=statebuild_trunk-to-dev-na
dataset=bfdata
frostbite_licensee=BattlefieldGame
elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1
elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core
workspace_root=D:\dev
azure_elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm
azure_elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1
azure_workspace_root=E:\dev
azure_fileshare=[additional_tools_to_include:[frostedtests, win64], secret_context:glacier_azure_fileshare, target_build_share:bfglacier]
report_build_version= --reporting-build-version-id %code_changelist%
pipeline_determinism_test_configuration={com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration: enabled: true, referenceJob: .data.start}
autotest_remote_settings=[eala:[p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001, p4_data_creds:bct-la-p4, p4_data_server:dicela-p4edge-fb.la.ad.ea.com:2001], dice:[p4_code_creds:perforce-battlefield01, p4_code_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001, p4_data_creds:perforce-battlefield01, p4_data_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001]]
deploy_frostedtests=true
deploy_tests=true
skip_code_build_if_no_changes=false
slack_channel_code=[channels:[#bf-trunk-to-dev-na-build-notify], skip_for_multiple_failures:true]
fake_ooa_wrapped_symbol=false
sndbs_enabled=true
slack_channel_data=[channels:[#bf-trunk-to-dev-na-build-notify], skip_for_multiple_failures:true]
enable_lkg_cleaning=true
poolbuild_data=true
skip_frosty_trigger=true
poolbuild_frosty=true
slack_channel_frosty=[channels:[#bf-trunk-to-dev-na-build-notify], skip_for_multiple_failures:true]
use_linuxclient=true
asset=DevLevels
enable_lkg_p4_counters=true
extra_data_args=[--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
extra_frosty_args=[--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
extra_icepick_args=--heartbeat-timeout 600
server_asset=Game/Setup/Build/DevMPLevels
shift_branch=true
shift_every_build=true
strip_symbols=false
skip_icepick_settings_file=true
timeout_hours_data=8
timeout_hours_frosty=8
branch_name=trunk-to-dev-na
project=class com.ea.project.bct.Bct
patchfrosty_matrix=[]
platform=tool
config=release
content_type=code
additional_tools_to_include=frostedtests, win64
target_build_share=bfglacier
secret_context=glacier_azure_fileshare
node_label=copy_build_to_azure</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist for upload.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>source</name>
                    <description>Skip path logic and use this path as source</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>destination</name>
                    <description>Skip path logic and use this path as destination</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>target_build_share</name>
                    <description>elipy config key to find buildshare in alternate_build_shares.</description>
                    <defaultValue>bfglacier</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>secret_context</name>
                    <description>Elipy config secrets where key for filer auth</description>
                    <defaultValue>glacier_azure_fileshare</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>additional_tools_to_include</name>
                    <description>Additional tool(s) to pull from network share</description>
                    <defaultValue>frostedtests, win64</defaultValue>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.project.GetMasterFile
import com.ea.project.GetBranchFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * azure_upload_scheduler.groovy
 */
pipeline {
    agent {
        node {
            label(env.node_label)
            customWorkspace(project.workspace_root)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.code_changelist])
            }
        }
        stage('Upload to Azure') {
            steps {
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                script {
                    String additionalTools = params.additional_tools_to_include
                    def optional_args = ''
                    optional_args += params.source ? ' --source ' + params.source : ''
                    optional_args += params.destination ? ' --destination ' + params.destination : ''
                    optional_args += additionalTools ? additionalTools.split(',').collect { " --additional-tools-to-include ${it.trim()}" }.join('') : ''
                    withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                        bat([env.elipy_call, 'copy_from_filer_to_azure',
                             '--content-type', env.content_type,
                             '--platform', env.platform,
                             '--config', env.config,
                             '--code-branch', env.code_branch,
                             '--code-changelist', params.code_changelist,
                             '--target-build-share', params.target_build_share,
                             '--secret-context', params.secret_context,
                             '--licensee', env.frostbite_licensee,
                             optional_args,
                        ].join(' '))
                    }
                }
            }
        }
    }
}

</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>