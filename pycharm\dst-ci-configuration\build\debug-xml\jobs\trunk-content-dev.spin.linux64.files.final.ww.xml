<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description/>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>code_folder=mainline
code_branch=trunk-content-dev
data_folder=mainline
data_branch=trunk-content-dev
koala_content=true
job_label_poolbuild=poolbuild_trunk-content-dev
job_label_statebuild=statebuild
dataset=bfdata
elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm --use-fbenv-core
elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1
frostbite_licensee=BattlefieldGame
workspace_root=D:\dev
azure_elipy_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm
azure_elipy_install_call=tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1
azure_workspace_root=E:\dev
azure_fileshare=[secret_context:glacier_azure_fileshare, target_build_share:bfglacier]
webexport_script_path=Code\DICE\BattlefieldGame\fbcli\webexport.py
autotest_remote_settings=[eala:[p4_code_creds:bct-la-p4, p4_code_server:dicela-p4edge-fb.la.ad.ea.com:2001], dice:[p4_code_creds:perforce-battlefield01, p4_code_server:dice-p4buildedge02-fb.dice.ad.ea.com:2001]]
fake_ooa_wrapped_symbol=false
skip_code_build_if_no_changes=true
slack_channel_code=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
statebuild_code=false
sndbs_enabled=true
deployment_data_branch=true
enable_daily_data_clean=true
enable_lkg_cleaning=true
poolbuild_data=true
slack_channel_data=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
statebuild_data=false
statebuild_webexport=false
timeout_hours_data=8
webexport_allow_failure=true
webexport_branch=true
timeout_hours_enlighten_job=12
frosty_reference_job=trunk-content-dev.deployment-data.start
poolbuild_frosty=true
slack_channel_frosty=[channels:[#bct-build-notify], skip_for_multiple_failures:true]
use_linuxclient=true
asset=DevLevels
bilbo_store_offsite=true
enable_custom_cl_preflight_code=true
deployment_data_reference_job=trunk-content-dev.data.start
drone_outsourcers=[PlanA, Jukebox]
enable_lkg_p4_counters=true
environment_variables=[ASAN_WIN_CONTINUE_ON_INTERCEPTION_FAILURE:1]
extra_data_args=[--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
extra_frosty_args=[--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ]
import_avalanche_autotest=false
offsite_basic_drone_zip_builds=true
offsite_code_token=<EMAIL>:1180acfb166f9612424e10e603d75acc0e
offsite_drone_basic_builds=true
offsite_drone_builds=true
offsite_job_remote=http://dice-la-jenkins-tools.la.ad.ea.com:8080/job/GetNewDrone_trunk-content-dev/buildWithParameters?token=2Zm67RaPGVd6^&amp;code_changelist=%code_changelist%^&amp;cause=%BUILD_URL%^&amp;share_root=\\filer.dice.ad.ea.com\Builds\Battlefield\code\trunk-content-dev
quickscope_db=kinpipeline
quickscope_import=true
remote_masters_to_receive_code=[[name:bct-preflight-jenkins.cobra.dre.ea.com, allow_failure:false]]
remote_masters_to_receive_data=[[name:bct-preflight-jenkins.cobra.dre.ea.com, allow_failure:false]]
reshift_offsitedrone=true
server_asset=Game/Setup/Build/DevMPLevels
shift_branch=true
shift_reference_job=trunk-content-dev.frosty.start
single_stream_smoke=true
skip_icepick_settings_file=true
strip_symbols=false
trigger_string_shift=TZ=Europe/Stockholm 
 H 1,14 * * 1-6
H 6,13 * * 7
trigger_string_shift_offsite_drone=TZ=Europe/London 
 H 16 * * 1-6
H 16 * * 7
trigger_type_shift=cron
upgrade_data_job=true
use_deprecated_blox_packages=true
move_location_parallel=true
new_locations=[earo:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location earo --use-fbenv-core], Guildford:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location Guildford --use-fbenv-core], Montreal:[elipy_call_new_location:tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location Montreal --use-fbenv-core]]
shift_subscription_matrix=[[dest_location:Guildford, build_type:code, job_label:24_core &amp;&amp; criterion, p4_code_creds:perforce-battlefield-criterion, p4_code_server:oh-p4edge-fb.eu.ad.ea.com:2001, p4_data_server:oh-p4edge-fb.eu.ad.ea.com:2001, src_location:DiceStockholm]]
branch_name=trunk-content-dev
project=class com.ea.project.bct.Bct
patchfrosty_matrix=[]
node_label=statebuild
platform=linux64
format=files
config=final
region=ww</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist for upload.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist for upload. Defaults to code_changelist.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.aws

import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * UploadToSpin.groovy
 */
pipeline {
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.node_label)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.data_changelist, params.code_changelist])
            }
        }
        stage('Upload to Spin') {
            steps {
                P4SyncDefault(
                    project,
                    branchFile,
                    env.code_folder,
                    env.code_branch,
                    'code',
                    params.code_changelist,
                )
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                    bat([env.elipy_call, 'submit_to_spin',
                        '--code-branch', env.code_branch,
                        '--code-changelist', params.code_changelist,
                        '--data-branch', env.data_branch,
                        '--data-changelist', params.data_changelist ?: params.code_changelist,
                        '--platform', env.platform,
                        '--format', env.format,
                        '--config', env.config,
                        '--region', env.region,
                    ].join(' '))
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchFile.standard_jobs_settings?.slack_channel_spin
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>