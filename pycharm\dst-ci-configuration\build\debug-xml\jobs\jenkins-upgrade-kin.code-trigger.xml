<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Sync jenkins-upgrade-kin code and deploy a binary build.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=jenkins-upgrade-kin
code_branch=kin-dev
code_folder=dev
codepreflight_info_sending=null
data_triggers_patchdata=false
enable_lkg_p4_counters=false
frostbite_syncer_setup=false
main_unverified_branch=false
non_virtual_code_branch=
non_virtual_code_folder=
project_name=kingston
retry_limit=2
skip_code_build_if_no_changes=true
smoke_cl_after_success=false
skip_frosty_scheduler=false
slack_notify_bot_code=false
slack_notify_bot_code_nomaster=false
slack_notify_bot_code_stressbulkbuild=false
code_reference_job=trigger-from.dice-kin-dev
skip_clean_label=false</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>false</string>
                            <string>true</string>
                        </a>
                    </choices>
                    <name>clean_local</name>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers/>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.testjobs

import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * code_trigger_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchfile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, ignore_paths, [], settings_map)
                }
            }
        }
        stage('Trigger code jobs') {
            steps {
                script {
                    def code_changelist = env.P4_CHANGELIST
                    def clean_local = params.clean_local

                    echo 'Code changelist synced from Perforce: ' + code_changelist

                    if (code_changelist == null) {
                        echo 'Missing code changelist, aborting build!'
                        currentBuild.result = Result.UNSTABLE.toString()
                        return
                    }

                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                    echo 'Clean local: ' + clean_local
                    echo 'Retry limit: ' + env.retry_limit

                    def cause_action = currentBuild.rawBuild.getAction(CauseAction)
                    if (cause_action.findCause(hudson.model.Cause.UpstreamCause)) {
                        echo 'Triggered by an upstream job.'
                    } else if (cause_action.findCause(hudson.model.Cause.UserIdCause)) {
                        echo 'Triggered manually by a user.'
                    } else if (cause_action.findCause(hudson.triggers.SCMTrigger.SCMTriggerCause)) {
                        echo 'Triggered by an SCM change.'
                    }
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>