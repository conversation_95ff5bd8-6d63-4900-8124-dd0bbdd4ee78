# pylint: disable=too-many-lines
"""
avalanche.py

Implements various functions against Avalanche.

Prefers avalanchecli over any direct HTTP calls, but won't shy away from invoking HTTP requests
when avalanchecli falls short (missing, hard-to-parse output, etc).

Assumes localhost:1338 is the Avalanche instance to talk to.
"""
from __future__ import absolute_import
from past.utils import old_div
from builtins import str
from deprecated import deprecated
import json
import os
import platform as os_platform
import re
import six
import datetime
import time
import requests
from typing import Dict, List, Optional, Union

# Workaround to run unit tests invoke winreg in Linux (gitlab runner)
if os_platform.system() == "Windows":
    import winreg  # pylint: disable=E0401
else:
    winreg = None  # pylint: disable=C0103


from elipy2 import (
    core,
    SETTINGS,
    LOGGER,
    windows_tools,
    local_paths,
    running_processes,
    frostbite_core,
)
from elipy2.exceptions import (
    ELIPYException,
    AvalancheException,
    WindowsServiceException,
)
from elipy2.exceptions import ConfigValueNotFoundException
from elipy2.telemetry import collect_metrics
from elipy2.avalanche_web_api.server import Server
from elipy2.avalanche_web_api.core import Core
from elipy2.avalanche_web_api.database import Database


@deprecated(version="8.1", reason="You should use avalanche_web_api.core.Core.get")
def _http_get(url):
    """
    Wrapper function for HTTP GET calls.
    """
    response = requests.get(url)
    LOGGER.info("GET {0}".format(url))
    if response.status_code != 200:
        raise ELIPYException("{0} returned HTTP code {1}".format(url, response.status_code))
    return response.json()


@deprecated(version="8.1", reason="You should use avalanche_web_api.core.Core.put")
def _http_put(url, data, header=None):
    """
    Wrapper function for HTTP PUT calls.
    """
    if header is None:
        header = {"content-type": "application/json"}
    response = requests.put(url, data=data, headers=header)
    LOGGER.info("PUT {0}, {1}".format(url, data))
    if response.status_code < 200 or response.status_code >= 300:
        raise ELIPYException("{0} returned HTTP code {1}".format(url, response.status_code))


@deprecated(version="8.1", reason="You should use avalanche_web_api.core.Core.delete")
def _http_delete(url):
    """
    Wrapper function to HTTP DELETE calls.
    """
    response = requests.delete(url)
    LOGGER.info("DELETE {0}".format(url))
    if response.status_code != 200:
        raise ELIPYException("{0} returned HTTP code {1}".format(url, response.status_code))
    return response.json()


@deprecated(version="8.1", reason="You should use avalanche_web_api.cache.Cache.put_cache_value")
@collect_metrics()
def set_cache_value(key, value, avalanche_url="http://localhost:1338"):
    """
    Stores key and value pair in Avalanche cache.
    """
    if isinstance(key, list):
        key = "/".join(key)
    url = avalanche_url + "/cache/" + key
    _http_put(url, value)


def set_avalanche_build_status(
    code_changelist,
    data_changelist,
    data_branch="fake_branch",
    platform="fake_platform",
):
    """
    Stores build status in Avalanche cache.
    """
    data = {
        "data_branch": data_branch,
        "data_changelist": data_changelist,
        "code_changelist": code_changelist,
        "platform": platform,
    }
    key_name = "{}_{}_{}".format(windows_tools.get_computer_name(), data_branch, platform)
    set_cache_value(["dre_buildfarm", key_name], json.dumps(data))


@deprecated(version="8.1", reason="You should use avalanche_web_api.cache.Cache.get_cache_value")
@collect_metrics()
def get_cache_value(key: Union[str, List], avalanche_url: str = "http://localhost:1338") -> Dict:
    """
    Stores key and value pair in Avalanche cache.

    :params key: A string or a list of strings that say what key to query
    :params avalanche_url: What Avalanche server should you interact with
    :returns: A dictionary containing the key data
    :rtype: Dictionary
    """
    server = Server(avalanche_url)

    cache_value_raw = server.cache.get_cache_value(key)
    cache_value = json.loads(cache_value_raw)

    return cache_value


@deprecated(version="8.1", reason="You should use avalanche_web_api.database.Database.get_dbs")
@collect_metrics()
def getdbs(avalanche_url: str = "http://localhost:1338/") -> List[Dict]:
    """
    Retrieves a list of all databases in $avalanche_url

    :params avalanche_url: What Avalanche server should you interact with
    :returns: A list of Dictionaries containing database information
    :rtype: List of dictionaries
    """
    server = Server(avalanche_url)
    dbs = server.database.get_dbs()["databases"]

    return dbs


@collect_metrics()
def nuke():
    """
    Runs avalanchecli nuke -y
    """
    LOGGER.info("Nuking local Avalanche instance.")
    cmd = [local_paths.get_avalanchecli_exe_path(), "nuke", "-y"]
    try:
        core.run(cmd, print_std_out=True)
    except Exception as exc:
        LOGGER.info("Nuke exited with {}. Trying to recover".format(exc))
        windows_tools.start_service("Avalanche")
        if getdbs():
            raise
    timeout = 0
    while windows_tools.query_service("Avalanche") == "Unknown" and timeout < 800:
        LOGGER.info("Status of Avalanche is not 'Running', waiting")
        time.sleep(5)
        timeout += 5


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.delete_database",
)
@collect_metrics()
def drop(dbname):
    """
    Runs avalanchecli drop $dbname
    """
    LOGGER.info("Dropping {0}".format(dbname))
    cmd = [local_paths.get_avalanchecli_exe_path(), "drop", dbname]
    core.run(cmd, print_std_out=True)


@collect_metrics()
def ddelta(head, base, out):
    """
    Runs avalanchecli ddelta $head $base $out
    """
    if os.path.exists(out):
        LOGGER.info("Found previous delta bundle directory at {0}, removing.".format(out))
        core.delete_folder(out)
    LOGGER.info("Creating directory at {0}".format(out))
    os.makedirs(out)
    LOGGER.info("Getting delta from {0} and {1} -> {2}".format(head, base, out))
    cmd = [local_paths.get_avalanchecli_exe_path(), "ddelta", head, base, out]
    core.run(cmd, print_std_out=True)


@collect_metrics()
def export(state, out):
    """
    Runs avalanchecli export $state $out
    """
    if os.path.exists(out):
        LOGGER.info("Found previous state directory at {0}, removing.".format(out))
        core.delete_folder(out)
    LOGGER.info("Creating directory at {0}".format(out))
    os.makedirs(out)
    LOGGER.info("Deploying state from {0} -> {1}".format(state, out))
    cmd = [local_paths.get_avalanchecli_exe_path(), "export", state, out]
    exit_code, stdout, stderr = core.run(cmd, print_std_out=True, capture_std_out=True)

    error_strings = ["failed", "Failed to retrieve SHA1", "Caught exception in export"]
    full_stderr = " ".join(stderr)
    error_string_found = any((err_str in full_stderr for err_str in error_strings))
    if error_string_found:
        single_fmt = "\n\t"
        double_fmt = single_fmt + "\t"
        raise AvalancheException(
            "`{0}` failed with{4}exit code: {1}{4}stdout: {5}{2}{4}stderr: {5}{3}".format(
                " ".join(cmd),
                exit_code,
                double_fmt.join(stdout),
                double_fmt.join(stderr),
                single_fmt,
                double_fmt,
            )
        )


@collect_metrics()
def clone_db(source_db_spec, target_db_spec):
    """
    Runs avalanchecli clone source_db_spec target_db_spec
    """
    LOGGER.info("Cloning {0} -> {1}".format(source_db_spec, target_db_spec))
    cmd = [
        local_paths.get_avalanchecli_exe_path(),
        "clone",
        source_db_spec,
        target_db_spec,
    ]
    core.run(cmd, print_std_out=True)


@deprecated(version="8.1", reason="You should use avalanche_web_api.database.Database.db_exists")
@collect_metrics()
def db_exists(dbname: str, avalanche_url: str = "http://localhost:1338/") -> bool:
    """
    Utility function that checks if a db exist with the name $dbname.

    :params dbname: Name of database to check
    :params avalanche_url: What Avalanche server should you interact with
    :returns: true if database exists, false if it doesn't
    :rtype: bool
    """
    server = Server(avalanche_url)
    database_exists = server.database.db_exists(dbname)
    return database_exists


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.db_exists_with",
)
@collect_metrics()
def db_contains(names: List[str]) -> bool:
    """
    Utility function that checks if a db exist with the name $dbname.

    :params names: List of required strings to find in db name
    :returns: true if db found and false if not
    :rtype: bool
    """
    server = Server("http://localhost:1338/")
    match_found = server.database.db_exists_with(names)
    return match_found


@collect_metrics()
def remote_clone_db(
    source_db,
    dest_db,
    dest_host,
    source_host=None,
    branch=None,
    limited_lifetime=False,
    push_built_levels=False,
    complete_clone=False,
):
    """
    Runs avalanchecli remoteclone $dbname to dest_host
    """
    LOGGER.info("Clone db {} to {} in {}".format(source_db, dest_db, dest_host))
    if source_host is None:
        computer = windows_tools.get_computer_name()
        source_host = computer + ":1338"
    cmd = [
        local_paths.get_avalanchecli_exe_path(),
        "remoteclone",
        "{}@{}".format(source_db, source_host),
        "{}@{}".format(dest_db, dest_host),
    ]
    if complete_clone:
        cmd = cmd + ["--complete"]
    if limited_lifetime and frostbite_core.minimum_fb_version(year=2019, version_nr=8):
        key_name = "avalanche_state_lifetime"
        lifetime = 5
        error_message = ". Falling back to default value of {}.".format(lifetime)
        try:
            lifetime = int(SETTINGS.get(key_name).get(branch or "default", lifetime))
        except ConfigValueNotFoundException:
            LOGGER.info("Could not find a setting for {}{}".format(key_name, error_message))
        except ValueError:
            LOGGER.info(
                "{} cannot be parsed as an integer for '{}' branch{}".format(
                    key_name, branch or "default", error_message
                )
            )
        cmd += ["--ephemeralLifetime", str(lifetime * 24)]
    core.run(cmd, print_std_out=True)

    if push_built_levels:
        data = {"Results": get_built_levels(source_db)}
        put_built_levels(dest_db, json.dumps(data), dest_host)


@collect_metrics()
def import_baselinedb(
    dbpath,
    dbname,
    prefix="baseline_",
    postfix="",
    avalanche_url="http://localhost:1338/",
):
    """
    Utility function that wraps the intricacies of managing baseline states in Avalanche.

    It will:
        - Check if database matching $prefix + $dbname + $postfix already exists.
            - If true, do nothing
            - If false:
                - Drop all databases matching $prefix.*
                - Import database $prefix + $dbname + $postfix from $import_from
    """
    # Clean old dbs
    clean_old_dbs(days_to_store=5)
    new_db_name = prefix + dbname + postfix
    current_dbs = getdbs(avalanche_url=avalanche_url)
    # Baseline has already been imported, do nothing.
    if db_exists(new_db_name, avalanche_url=avalanche_url):
        LOGGER.info("Database {0} already imported, skipping import.".format(new_db_name))
        return

    regex = re.compile("{0}{1}.*".format(prefix, dbname), re.IGNORECASE)
    for database in current_dbs:
        if regex.match(database["id"]) is not None:
            LOGGER.info("Dropping {}".format(database["id"]))
            drop(database["id"])

    importdb(dbpath, new_db_name)


@collect_metrics()
def importdb(dbpath, dbname):
    """
    Runs avalanchecli import $dbname $dbpath
    """
    LOGGER.info("Importing {0} to Avalanche as {1}".format(dbpath, dbname))
    cmd = [local_paths.get_avalanchecli_exe_path(), "import", dbname, dbpath]
    core.run(cmd, print_std_out=True)


@collect_metrics()
def deploy(
    dbname: str,
    platform: str,
    destination: str,
    extra_args: Optional[List[str]] = None,
    include_platform: Optional[bool] = True,
    ordering_algorithm: Optional[str] = "breadthFirst",
):
    """
    Runs avalanchecli deploy $dbname $destination $platform $ordering_algorithm
    """
    LOGGER.info("Deploying CAS bundles from {0} to {1}".format(dbname, destination))
    if not os.path.exists(destination):
        LOGGER.info("{} does not exist, creating directory.".format(destination))
        os.makedirs(destination)

    cmd = [
        local_paths.get_avalanchecli_exe_path(),
        "deploy",
        dbname,
        destination,
    ]

    if include_platform:
        avalanche_platform = get_avalanche_platform_name(platform)
        cmd.append(avalanche_platform)

    if ordering_algorithm:
        if not include_platform:
            raise ELIPYException(
                "Not possible to specify an ordering algorithm if we skip platform."
            )
        else:
            cmd.append(ordering_algorithm)

    if extra_args:
        cmd += extra_args

    core.run(cmd, print_std_out=True)


@collect_metrics()
def combine(
    input_dir_1: str,
    input_dir_2: str,
    output_dir: str,
    extra_combine_args: Optional[List[str]] = None,
) -> None:
    """
    Runs avalanchecli combine
    Combines two data bundle directories into one.

    :param input_dir_1: Path to the first input bundle directory.
    :param input_dir_2: Path to the second input bundle directory.
    :param output_dir: Path to the output bundle directory.
    :param extra_combine_args: Extra args for the combine process.
    :return: None
    """
    if not extra_combine_args:
        extra_combine_args = []
    cmd = [
        local_paths.get_avalanchecli_exe_path(),
        "combine",
        output_dir,
        input_dir_1,
        input_dir_2,
    ] + extra_combine_args
    core.run(cmd, print_std_out=True)


@collect_metrics()
def export_avalanche_state(
    builder,
    branch,
    data_changelist,
    code_changelist,
    filer=None,
    deploy_state_to_bundles=False,
):
    """
    Exports Avalanche state and copies it to filer
    """
    LOGGER.info("Exporting Avalanche state...")
    start_time = time.time()

    # Copy state to a temp DB ('make sure to export temp assets in the temp domain')
    db_name = get_temp_db_name(builder.platform, data_changelist, code_changelist, branch)
    builder.cook(pipeline_args=["-exportState", db_name])
    LOGGER.info("Pipeline tool export successful.")

    remote_host = []
    if frostbite_core.minimum_fb_version(year=2021, version_nr=1):
        try:
            source_host = windows_tools.get_computer_name()
            dest_host = SETTINGS.get("avalanche_state_host")[builder.platform]
            remote_clone_db(
                source_db=db_name,
                dest_db=db_name,
                dest_host=dest_host,
                source_host=source_host,
                push_built_levels=False,
                complete_clone=True,
            )
            remote_host = [dest_host]
        except ConfigValueNotFoundException:
            LOGGER.info("Not setup to run remote cloning of state in elipy config")

    # Export Avalanche state to local folder via avalanchecli
    local_path = local_paths.get_local_avalanche_export_path(
        branch, data_changelist, code_changelist, builder.platform
    )
    export(db_name, local_path)
    LOGGER.info("Avalanche export successful.")

    # Copy local Avalanche state to filer
    if filer:
        filer.deploy_avalanche_state(
            builder.platform, branch, data_changelist, code_changelist, remote_host
        )
        if deploy_state_to_bundles:
            filer.deploy_state(
                local_path,
                data_branch=branch,
                data_changelist=data_changelist,
                code_branch=branch,
                code_changelist=code_changelist,
                platform=builder.platform,
            )
    # Drop temp DB
    drop(db_name)

    # Delete locally exported state
    clean_temp_state_folder(local_path)
    LOGGER.info("Export successful. Took {0:.0f} seconds.".format(time.time() - start_time))


# pylint: disable=inconsistent-return-statements
@collect_metrics()
def reimport_needed(platform, branch, data_changelist, changelist_limit=300):
    """
    Checks if a reimport is needed, values and checks here will probably need to get tweaked.
    """
    try:
        key_name = "{}_{}_{}".format(windows_tools.get_computer_name(), branch, platform)
        last_built_data = get_cache_value(["dre_buildfarm", key_name])
    except Exception as exc:
        LOGGER.info("Failed to read last build data from Avalanche cache: {}".format(exc))
        return True

    if "data_changelist" in last_built_data:
        LOGGER.info(
            "Last data changelist built on machine is {}, now building {}".format(
                last_built_data["data_changelist"], data_changelist
            )
        )
        if changelist_limit > abs(int(data_changelist) - int(last_built_data["data_changelist"])):
            if db_exists(get_full_database_name(platform)):
                LOGGER.info(
                    "Not importing state since machine recently built on {} for {}".format(
                        branch, platform
                    )
                )
                return False
            else:
                LOGGER.info("Db does not exist in Avalanche anymore, need to import state.")
    return True


@collect_metrics()
def import_avalanche_state(
    platform,
    branch,
    data_changelist,
    code_changelist,
    _filer,
    only_new_code=False,
    current_data_changelist=None,
    _data=None,
    avalanche_url="http://localhost:1338/",
):
    """
    Copies an exported Avalanche state from filer to local and imports it.
    """
    db_name = get_temp_db_name(platform, data_changelist, code_changelist, branch)
    if current_data_changelist and not reimport_needed(platform, branch, current_data_changelist):
        return []
    if db_exists(db_name):
        LOGGER.info("Skipped importing Avalanche state, already have {0}".format(db_name))
        return []
    elif only_new_code and db_contains([code_changelist, platform, "temp"]):
        LOGGER.info(
            "Skipped importing Avalanche state, already have state with code cl {0}".format(
                code_changelist
            )
        )
        return []
    else:
        LOGGER.info("Importing previous Avalanche state...")
        # Copy local Avalanche state from filer
        try:
            _filer.fetch_avalanche_state(platform, branch, data_changelist, code_changelist)
            if _data:
                _data.run_indexing()
            # This method will only import of we don't have it already, and will clean old
            # ones if a new one is imported.
            local_path = local_paths.get_local_avalanche_export_path(
                branch, data_changelist, code_changelist, platform
            )
            import_baselinedb(
                local_path,
                platform,
                prefix=get_temp_db_prefix(),
                postfix=get_temp_db_postfix(data_changelist, code_changelist)
                + "_{}".format(branch),
                avalanche_url=avalanche_url,
            )
            LOGGER.info("AvalancheCLI import successful.")

            clean_temp_state_folder(local_path)
            return ["-importState", db_name]
        except Exception as exp:
            LOGGER.error("Error while importing state: {}".format(exp))
            return []


@collect_metrics()
def clean_temp_state_folder(local_path):
    """
    Help function for making sure the temp Avalanche state folder is deleted.
    """
    try:
        # delete copied state from filer
        core.delete_folder(local_path)
    except Exception:
        running_processes.kill(["AvalancheCLI.exe"])
        core.delete_folder(local_path)


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.get_built_levels",
)
@collect_metrics()
def get_built_levels(
    dbname: str,
    avalanche_url: str = "http://localhost:1338",
    to_file: Optional[str] = None,
) -> List[Dict]:
    """
    Fetches builtlevels from $dbname and returns the result object.
    Can optionally write to $to_file as well.

    :params dbname: Name of Avalanche database
    :params avalanche_url: What Avalanche server should you interact with
    :params to_file: Target file to write to if set
    :returns: A list of dictionaries contain the build level details
    :rtype: List of dictionaries
    """
    LOGGER.info("Fetching built levels from {} at {}".format(dbname, avalanche_url))

    server = Server(avalanche_url)
    built_levels = server.database.get_built_levels(dbname)

    LOGGER.info("Built levels:")
    for level in built_levels["Results"]:
        LOGGER.info(level["Level"])

    if to_file:
        dir_name = os.path.dirname(to_file)
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
        LOGGER.info("Writing built levels information to {0}".format(to_file))
        with open(to_file, "w") as _file:
            _file.write(json.dumps(built_levels))

    return built_levels["Results"]


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.put_built_levels",
)
@collect_metrics()
def put_built_levels(dbname, data, avalanche_url="http://localhost:1338"):
    """
    Puts builtlevels data to specified db location.
    """
    url = six.moves.urllib.parse.urljoin(
        avalanche_url, "db/{0}/kv/drone/builtlevels".format(dbname)
    )
    _http_put(url, data)


@deprecated(version="8.1", reason="You should use avalanche_web_api.database.Database.delete_db")
@collect_metrics()
def drop_build_record(record, avalanche_url="http://localhost:1338"):
    """
    Deletes/Drops database records
    """
    url = six.moves.urllib.parse.urljoin(avalanche_url, "db/{0}".format(record))
    response = _http_delete(url)
    return response["deleted"]


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.get_ops_chain",
)
@collect_metrics()
def get_ops_chain(dbname, avalanche_url="http://localhost:1338", to_file=None):
    """
    Fetches opchain for $dbname and writes to $tofile
    """
    if to_file is None:
        raise ELIPYException("Need to_file to GET ops chain (can't fit in memory).")

    url = six.moves.urllib.parse.urljoin(avalanche_url, "db/{0}/ops/chain".format(dbname))

    LOGGER.info("GET {0}".format(url))
    request = requests.get(url, stream=True)
    request.raise_for_status()

    LOGGER.info("Writing ops chain to {0}".format(to_file))
    with open(to_file, "wb") as _file:
        for block in request.iter_content(1024):
            _file.write(block)

    LOGGER.info("Wrote {0} to {1}".format(url, to_file))


def get_local_avalanche():
    """
    :returns: local Avalanche database url
    """
    computer = windows_tools.get_computer_name()
    url = "http://" + computer + ":1338/Avalanche"
    return url


@deprecated(version="8.1", reason="You should use avalanche_web_api.core.Core.post")
@collect_metrics()
def avalanche_invoke(url, post):
    """
    Calling mechanism for Avalanche.
    """
    request = six.moves.urllib.request.Request(url)
    request.add_header("Content-Type", "application/json")
    with six.moves.urllib.request.urlopen(request, json.dumps(post).encode()) as response:
        LOGGER.info("Response with payload: {0}".format(response.read().decode()))


@collect_metrics()
def avalanche_status():
    """
    Will print out current status of Avalanche

    :returns: A dictionary containing Avalanche status information
    :rtype: Dictionary
    """
    computer = windows_tools.get_computer_name()
    avalanche_url = f"http://{computer}:1338"
    server = Server(avalanche_url)
    primary_pool_info = server.storage.get_pool_primary_info()

    LOGGER.info("http://{0}:1338/admin/#/home".format(computer))

    alist = [
        "totalDiskSpace",
        "freeDiskSpace",
        "extentCount",
        "extentAvailBytes",
        "overflowSize",
    ]
    temp = []

    for option in alist:
        temp.append(
            windows_tools.convert_bytes_to_human_readable(
                int(primary_pool_info["Primary"]["pools"][0][option])
            )
        )

    LOGGER.info("{0} Information".format(computer))
    LOGGER.info("Total Disk Space: {0}".format(temp[0]))
    LOGGER.info("Free Disk Space: {0}".format(temp[1]))
    LOGGER.info("Extend Size: {0} GB".format(temp[2]))
    LOGGER.info("Avaliable Extend: {0}".format(temp[3]))
    LOGGER.info("Current Overflow: {0}".format(temp[4]))
    return_data = {
        "totalDiskSpace": int(primary_pool_info["Primary"]["pools"][0]["totalDiskSpace"]),
        "freeDiskSpace": int(primary_pool_info["Primary"]["pools"][0]["freeDiskSpace"]),
        "extentCount": int(primary_pool_info["Primary"]["pools"][0]["extentCount"]),
        "extentAvailBytes": int(primary_pool_info["Primary"]["pools"][0]["extentAvailBytes"]),
        "overflowSize": int(primary_pool_info["Primary"]["pools"][0]["overflowSize"]),
    }
    return return_data


@collect_metrics()
def clean_old_dbs(days_to_store=6):
    """
    Will clean out all databases not updated within days to keep

    :params days_to_store: Number of days to key the db for
    :returns: None
    :rtype: None
    """
    LOGGER.info("*********************************************************************")
    LOGGER.info("*************** Cleaning up old Avalanche databases *****************")
    LOGGER.info("*********************************************************************")

    computer = windows_tools.get_computer_name()
    avalanche_url = f"http://{computer}:1338"
    server = Server(avalanche_url)
    all_dbs = server.database.get_db_all()

    for item in all_dbs:
        dbid = item["id"]
        updated = item["updated"]
        now = datetime.datetime.now()
        diff = now - datetime.datetime.strptime(updated, "%Y-%m-%dT%H:%M:%SZ")
        if diff.days > days_to_store:
            LOGGER.info("Dropping {}, updated {} ago".format(dbid, diff.days))
            drop(dbid)
        LOGGER.info("Keeping {}, updated {} ago".format(dbid, diff.days))

    LOGGER.info("Done")
    LOGGER.info("*********************************************************************")


@collect_metrics()
def clean_empty_dbs(database):
    """
    Will clean out all databases which has oplogSize=0
    """
    LOGGER.info("Cleaning up empty Avalanche databases")
    return database.delete_empty_databases()


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.delete_all_databases",
)
@collect_metrics()
def drop_all_dbs():
    """
    Drop all databases in Avalanche.
    """
    LOGGER.info("*********************************************************************")
    LOGGER.info("*************** Dropping all Avalanche databases ********************")
    LOGGER.info("*********************************************************************")

    computer = windows_tools.get_computer_name()
    url = "http://" + computer + ":1338/db/all"
    with six.moves.urllib.request.urlopen(url) as response:
        data_job = response.read().decode("utf-8")
    data = json.loads(data_job)

    for item in data:
        dbid = item["id"]
        LOGGER.info("Dropping {}".format(dbid))
        drop(dbid)

    LOGGER.info("Done")
    LOGGER.info("*********************************************************************")


@collect_metrics()
def cleanup_temp_dbs(
    min_retention=5,
    cleanup_threshold=15,
    disk_space_threshold=50,
    avalanche_space_threshold=20,
    temp_name_pattern="temp",
    computer=None,
):
    """
    Remove temp dbs to free up disk space

    If we have more than `cleanup_threshold` temp Avalanche DBs, and we have less than
    `disk_space_threshold` space remaining, delete all but `min_retention` temp dbs

    :param min_retention: int The number of databases to leave when running cleanup
    :param cleanup_threshold: int The number of databases required before cleaning up databases
    :param disk_space_threshold: int threshold for disk space remaining
    :param temp_name_pattern: string Regex value for matching temp Avalanche db names
    :returns: None
    """
    LOGGER.info("*********************************************************************")
    LOGGER.info("*************** Cleaning up old Avalanche databases *****************")
    LOGGER.info("*********************************************************************")
    computer = computer or windows_tools.get_computer_name()
    free_gbs = windows_tools.get_free_disk_space(computer=computer)
    LOGGER.info("   Remaining space on C drive: {}GBs".format(free_gbs))
    data = get_db_all_data(computer)

    regex = re.compile(temp_name_pattern)
    temp_dbs = [d for d in data if regex.match(d["id"])]
    sorted_data = sorted(temp_dbs, key=lambda x: x["updated"])

    space_threshold_met = free_gbs < disk_space_threshold
    db_threshold_met = len(sorted_data) > cleanup_threshold

    avalanche_data = avalanche_status()
    avalanche_space_gb = old_div(avalanche_data["extentAvailBytes"], 1 << 30)
    avalanche_threshold_met = avalanche_space_gb < avalanche_space_threshold

    should_clean_up_dbs = (  # pylint: disable=consider-using-ternary
        space_threshold_met and db_threshold_met
    ) or avalanche_threshold_met

    dbs_to_drop = []
    if should_clean_up_dbs:
        dbs_to_drop = sorted_data[min_retention:]

    for item in dbs_to_drop:
        dbid = item["id"]
        LOGGER.info("Dropping {}".format(dbid))
        drop(dbid)

    LOGGER.info("Done")
    LOGGER.info("*********************************************************************")


@deprecated(
    version="8.1",
    reason="You should use avalanche_web_api.database.Database.get_db_all",
)
@collect_metrics()
def get_db_all_data(computer):
    """
    Make a request to Avalanche to get the all database stats

    Make a get request of the target server and return the output from /db/all

    :param computer: string Avalanche server to make the request at
    :returns: dict the json response converted to a dictionary
    """

    url = "http://" + computer + ":1338/db/all"
    with six.moves.urllib.request.urlopen(url) as response:
        data_job = response.read().decode("utf-8")
    data = json.loads(data_job)
    return data


@collect_metrics()
def check_avalanche_space(
    percent_required_free=15, storage_pool=0, avalanche_url="http://localhost:1338"
):
    """
    Checks that machine has more than percentRequiredFree free for storage_pool
    """
    percent_space_left = get_avalanche_free_space(storage_pool, avalanche_url)
    if percent_space_left < percent_required_free:
        raise WindowsServiceException(
            str(percent_space_left) + " % space left.  More space needed!"
        )


@collect_metrics()
def get_avalanche_free_space(storage_pool, avalanche_url):
    """
    Does the call to check percentage and returns a % out of 100.
    """
    storage_pool = six.moves.urllib.parse.urljoin(
        avalanche_url, "storage/pools/{}/info".format(storage_pool)
    )
    with six.moves.urllib.request.urlopen(storage_pool) as resource:
        response = json.load(resource)
        if response["spillExtent"]["totalPayload"] > 0:
            raise AvalancheException(
                "This Machine is in "
                + str(response["spillExtent"]["totalPayload"])
                + " bytes of overflow!"
            )

        percent_space_left = int(
            old_div(float(response["available"]), float(response["capacity"])) * 100
        )
    return percent_space_left


@collect_metrics()
def avalanche_blocker_check():
    """
    Checks running tasks and raises exception if running any maintenance jobs.
    """
    hostname = windows_tools.get_computer_name()
    recent_tasks = _http_get(f"http://{hostname}:1338/tasks/recent")
    for _, value in enumerate(recent_tasks):
        job = value["description"]
        if re.match("^Collecting Garbage|Evacuating Overflow|Rebalancing storage pools*", job):
            if value["state"] == "running":
                raise AvalancheException(f"Found running {job} maintenance job, stopping run")
    LOGGER.info("No running maintenance jobs found on {0}, continuing run".format(hostname))


@collect_metrics()
def avalanche_maintenance(drop_old_dbs=False):
    """
    Full Avalanche Maintenance run
    Will perform checks on the service and output
    all details before and after the maintenance cycle
    """

    # Available jobs: collectGarbage, trimExtents, evacuateOverflow, rebalance
    LOGGER.info("*************** Starting Avalanche Maintenance **********************")
    LOGGER.info("Checking if Avalanche is running")
    avalance_status = windows_tools.query_service("Avalanche")
    if avalance_status != "Running":
        LOGGER.warning("Avalanche is : {0}".format(avalance_status))
        raise AvalancheException("Avalanche is not running - skipping node")
    else:
        LOGGER.info("Avalanche is : {0}".format(avalance_status))

    LOGGER.info("************  Avalanche Status Before Maintenance *******************")
    # Checking size before running maintenance
    computer = windows_tools.get_computer_name()
    url = "http://" + computer + ":1338/storage/pools/primary.json"
    avalanche_status()
    LOGGER.info("*********************************************************************")

    clean_empty_dbs(Database(Core("http://" + computer + ":1338")))

    # Drop databases not updated recently
    if drop_old_dbs:
        clean_old_dbs(days_to_store=6)

    run_garbage_collect()

    # The jobs specifically need the storage service to run.
    # Iterating through jobs to run them all, Collect Garbage seems
    # to do this anyway, so these will be quick.
    jobs = ["trimExtents", "evacuateOverflow", "rebalance"]
    for job in jobs:
        storage_service = "Storage"
        url = "http://" + computer + ":1338/" + storage_service
        LOGGER.info("Running Avalanche Maintenance: {0}".format(job))
        post_me = {
            "method": job,
            "params": {"limit": 600},  # Small limit, very quick jobs.
        }
        avalanche_invoke(url, post_me)
        LOGGER.info("{0} Completed".format(job))

    LOGGER.info("************  Avalanche Status after Maintenance *******************")
    # Checking size after running maintenance
    avalanche_data = avalanche_status()
    LOGGER.info("*********************************************************************")

    if drop_old_dbs:
        avalanche_space_gb = old_div(avalanche_data["extentAvailBytes"], 1 << 30)
        if avalanche_space_gb < 100:
            LOGGER.info("Avalanche free space is low, running extra clean up")
            if avalanche_space_gb < 40:
                clean_old_dbs(days_to_store=1)
            else:
                clean_old_dbs(days_to_store=3)
            run_garbage_collect()
    return 0


@collect_metrics()
def check_avalanche_service():
    """
    Check Avalanche service status
    Return: True for Running; False for anything else
    """
    win_avalance_status = windows_tools.query_service("Avalanche")
    LOGGER.info("Windows Avalanche service is: {0}".format(win_avalance_status))
    return win_avalance_status == "Running"


@deprecated(version="8.1", reason="You should use avalanche_web_api.server.Server.get_status")
@collect_metrics()
def check_avalanche_service_api(avalanche_url="http://localhost:1338"):
    """
    Double check Avalanche service status via API endpoint
    Return: True for Running; False for anything else
    """
    try:
        url = six.moves.urllib.parse.urljoin(avalanche_url, "Avalanche/status")
        api_avalance_status = _http_get(url)["summary"]
        LOGGER.info("Avalanche API service is: {0}".format(api_avalance_status))
    except Exception as exc:
        LOGGER.info("Avalanche API service response: {0}".format(exc))
        api_avalance_status = "unavailable"
    return api_avalance_status == "Running"


@collect_metrics()
def restart_avalanche(avalanche_url="http://localhost:1338"):
    """
    Restart Avalanche service if service itself is not Running.
    """
    if not (check_avalanche_service_api(avalanche_url) and check_avalanche_service()):
        LOGGER.warning("Restart Avalanche service ...")
        windows_tools.start_service("Avalanche")
        timeout = 0
        while windows_tools.query_service("Avalanche") == "Unknown" and timeout < 800:
            LOGGER.info("Status of Avalanche is not 'Running', waiting")
            time.sleep(5)
            timeout += 5


@collect_metrics()
def stop_avalanche(avalanche_url="http://localhost:1338"):
    """
    Stop Avalanche service if service itself is Running.
    """
    if check_avalanche_service_api(avalanche_url) or check_avalanche_service():
        LOGGER.info("Stopping Avalanche service ...")
        windows_tools.shutdown_service("Avalanche")
        return

    LOGGER.info("Avalanche service already stopped")


@deprecated(version="8.1", reason="You should use avalanche_web_api.storage.Storage.force_gc")
@collect_metrics()
def run_garbage_collect():
    """
    Runs garbage collect
    GarbageCollection specifically needs the Avalanche service to run.
    """
    computer = windows_tools.get_computer_name()
    url = "http://" + computer + ":1338/Avalanche"
    LOGGER.info("Running Avalanche Maintenance: collectGarbage")
    garbage_post = {
        "method": "collectGarbage",
        "params": {
            "force": True,  # Only GC uses force.
            # Set to 90 minutes (same as in ansible 1.5hr maintenance window),
            # can exit earlier if finish before limit
            "limit": 5400,
        },
    }
    avalanche_invoke(url, garbage_post)
    LOGGER.info("collectGarbage Completed")


@collect_metrics()
def get_avalanche_db_platform_map():
    """
    Return db_platform map platforms as a list.
    """
    db_platform_map = {
        "xb1": "Gen4a",
        "ps4": "Gen4b",
        "pc": "Win32",
        "win64": "Win32",
        "win32": "Win32",
        "win64game": "Win32",
        "win64server": "DedicatedServer",
        "server": "DedicatedServer",
        "linuxserver": "DedicatedServer",
        "dedicatedserver": "DedicatedServer",
        "linux": "Linux",
        "linux64": "Linux",
        "gen4a": "Gen4a",
        "gen4b": "Gen4b",
        "xbsx": "Xbsx",
        "ps5": "Ps5",
        "nx": "nx",
    }
    return db_platform_map


@collect_metrics()
def get_avalanche_platform_name(platform):
    """
    Converts the more commonly used names for our platforms to the ones Avalanche supports
    and expects.
    """
    db_platform_map = get_avalanche_db_platform_map()
    return db_platform_map[platform.lower()]


def get_reverse_avalanche_platform(platform):
    """
    Converts the more commonly used names for our platforms to the ones bilbo supports
    and expects.
    """
    db_platform_map = get_avalanche_db_platform_map()
    platforms = {key for key, value in db_platform_map.items() if value.lower() == platform.lower()}
    return platforms


def get_full_database_name(platform):
    """
    :returns: the currently set FB database name including platform.
    """
    return os.environ.get("fb_default_platform_db").format(get_avalanche_platform_name(platform))


def get_short_database_name():
    """
    :returns: the currently set FB database name excluding platform.
    """
    return os.environ.get("fb_default_database")


@collect_metrics()
def clean_avalanche(database_name):
    """
    Use avalanchecli to clean database.
    """
    cmd = ["avalanchecli.exe", "clean", database_name]
    LOGGER.info(cmd)
    return core.run(cmd, print_std_out=True)


def get_avalanche_db(platform):
    """
    :returns: the Avalanche db name
    """
    db_id = get_database_id()
    bid = get_fb_branch_id()
    bid = "" if bid == "default" else ("." + bid)
    platform = "." + get_avalanche_platform_name(platform)
    return db_id + bid + platform


def get_avalanche_db_valid(platform):
    """
    :returns: the Avalanche db name
    """
    db_id = get_database_id()
    bid = get_fb_branch_id()
    bid = "" if bid == "default" else ("." + bid)
    platform = "." + platform
    return db_id + bid + platform


def get_database_id():
    """
    Get database ID from the database.dbmanifest file.
    """
    with open(os.path.join(frostbite_core.get_game_data_dir(), "database.dbmanifest")) as manifest:
        for line in manifest.readlines():
            ink = re.search('<Database.+id="([^"]+)"', line)
            if ink:
                return ink.group(1)
    LOGGER.error("Failed to get <Database id=" "/> attribute from dbmanifest")
    return 0


def get_fb_branch_id():
    """
    :returns: the fb branch id for the node running the command.
    """
    fb_branch = os.environ.get("fb_branch_id")
    if fb_branch:
        return fb_branch
    else:
        raise ELIPYException("Unable to find fb_branch_id set in the environment.")


def get_temp_db_prefix():
    """
    Get temp database prefix
    """
    return "temp_"


def get_temp_db_postfix(data_changelist, code_changelist):
    """
    Generate a postfix for an Avalanche database name.
    """
    return "_{}_{}".format(data_changelist, code_changelist)


def get_temp_db_name(platform, data_changelist, code_changelist, data_branch=None, prefix=None):
    """
    Generate a temp Avalanche database name.
    """
    if not prefix:
        prefix = get_temp_db_prefix()
    else:
        prefix = prefix + "_"
    if not frostbite_core.minimum_fb_version(
        year=2019, version_nr=8
    ) and frostbite_core.minimum_fb_version(year=2019, version_nr=1):
        prefix = "{}db".format(get_temp_db_prefix())

    temp_db_name = get_db_name(platform, data_changelist, code_changelist, prefix=prefix)
    if not data_branch is None:
        temp_db_name += "_{}".format(data_branch)
    return temp_db_name


def get_db_name(platform, data_changelist, code_changelist, prefix="db"):
    """
    Generate an Avalanche database name
    """

    db_name = "{}{}{}".format(
        prefix, platform, get_temp_db_postfix(data_changelist, code_changelist)
    )
    if not frostbite_core.minimum_fb_version(
        year=2019, version_nr=8
    ) and frostbite_core.minimum_fb_version(year=2019, version_nr=1):
        avalanche_platform = platform
        if platform == "server":
            avalanche_platform = "dedicatedserver"

        db_name = "{}.{}_{}.{}".format(prefix, data_changelist, code_changelist, avalanche_platform)

    return db_name


def get_autotest_temp_db_name(platform, category_name, data_changelist, code_changelist):
    """
    Get the autotest temp db name
    """
    temp_db_name = get_temp_db_name(platform, data_changelist, code_changelist)

    temp_db_name = "{}.autotest.{}".format(temp_db_name, category_name)
    return temp_db_name


def disable_maintenance():
    """
    [Windows Only] Update Avalanche maintenance settings using registry key.
    """
    if os_platform.system() == "Windows":
        LOGGER.info("Disabling Avalanche maintenance")
        key_path = "Software\\WOW6432Node\\Frostbite\\Avalanche\\Avalanche"
        try:
            with winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
            ) as key:
                winreg.SetValueEx(key, "MaintenanceTimeOfDay", 0, winreg.REG_DWORD, 9999)
                winreg.SetValueEx(key, "MaintenanceWindowMinutes", 0, winreg.REG_DWORD, 90)
        except FileNotFoundError:
            with winreg.CreateKeyEx(
                winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
            ) as key:
                winreg.SetValueEx(key, "MaintenanceTimeOfDay", 0, winreg.REG_DWORD, 9999)
                winreg.SetValueEx(key, "MaintenanceWindowMinutes", 0, winreg.REG_DWORD, 90)
    else:
        raise ELIPYException("Attempt to update registry key in non-Windows platform.")


def disable_upstream_sets():
    """
    [Windows Only] Disable Avalanche upstream set using registry key.
    """
    if os_platform.system() == "Windows":
        LOGGER.info("Disabling upstream SETs")
        key_path = "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\CacheServer"
        with winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
        ) as key:
            winreg.SetValueEx(key, "PropagateSets", 0, winreg.REG_DWORD, 0)
    else:
        raise ELIPYException("Attempt to update registry key in non-Windows platform.")


def set_upstream_node(upstream_node):
    """
    [Windows Only] Update Avalanche upstream node using registry key.
    """
    if os_platform.system() == "Windows":
        LOGGER.info("Setting Avalanche upstream node: {}".format(upstream_node))
        key_path = "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\CacheServer"
        with winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
        ) as key:
            winreg.SetValueEx(key, "UpstreamNodes", 0, winreg.REG_MULTI_SZ, [upstream_node])
    else:
        raise ELIPYException("Attempt to update registry key in non-Windows platform.")


def update_storage_settings(path=None, size=None):
    """
    [Windows Only] Update Avalanche storage settings using registry key.
    """
    if os_platform.system() == "Windows":
        LOGGER.info("Setting storage pool path to {} with {} GB capacity".format(path, size))
        key_path = "SOFTWARE\\WOW6432Node\\Frostbite\\Avalanche\\Storage\\StoragePools\\0"
        with winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
        ) as key:
            if path:
                winreg.SetValueEx(key, "Path", 0, winreg.REG_SZ, path)
            if size:
                winreg.SetValueEx(key, "CapacityGB", 0, winreg.REG_DWORD, size)
    else:
        raise ELIPYException("Attempt to update registry key in non-Windows platform.")
