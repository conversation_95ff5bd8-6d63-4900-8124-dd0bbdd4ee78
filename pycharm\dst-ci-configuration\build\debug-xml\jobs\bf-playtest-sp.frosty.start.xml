<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Scheduler to orchestrate game build packaging on bf-playtest-sp .</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>0</maxConcurrentPerNode>
            <maxConcurrentTotal>1</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers/>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>False</string>
                            <string>True</string>
                        </a>
                    </choices>
                    <name>clean_data</name>
                    <description>If True, Avalanche will be cleaned at the beginning of the run.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=bf-playtest-sp
code_branch=trunk-code-dev
combine_reference_job=
data_branch=bf-playtest-sp
data_folder=mainline
dataset=bfdata
frostbite_syncer_setup=true
frosty_quick_job=
frosty_reference_job=trunk-code-dev.data.start
linux_docker_images=false
main_unverified_branch=false
frosty_only_build_on_new_code=false
non_virtual_data_branch=trunk-content-dev
non_virtual_data_folder=
project_name=bct
retry_limit=1
enable_lkg_p4_counters=false</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.frosty

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import hudson.model.Result
import jenkins.model.Jenkins

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

def is_triggered_by_code = (
    branchfile.code_downstream_matrix?.join(',')?.contains('.frosty') || env.frosty_reference_job.contains('.code.start')
)

/**
 * frosty_start.groovy
 */
pipeline {
    agent { label '(scheduler &amp;&amp; master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    if (is_triggered_by_code) {
                        def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                        P4PreviewData(project, 'stream', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                    }
                }
            }
        }
        stage('Trigger the frosty job') {
            steps {
                script {
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.frosty_reference_job)
                    def last_good_data = LibJenkins.getLastStableDataChangelist(env.frosty_reference_job)

                    if (env.frosty_quick_job) {
                        if (Jenkins.get().getItem(env.frosty_quick_job)?.building) {
                            last_good_code = Jenkins.get().getItem(env.frosty_quick_job).lastBuild.getEnvironment(TaskListener.NULL).code_changelist
                            last_good_data = Jenkins.get().getItem(env.frosty_quick_job).lastBuild.getEnvironment(TaskListener.NULL).data_changelist
                        } else {
                            last_good_code = LibJenkins.getLastStableCodeChangelist(env.frosty_quick_job)
                            last_good_data = LibJenkins.getLastStableDataChangelist(env.frosty_quick_job)
                        }
                    }

                    def code_changelist = params.code_changelist ?: last_good_code
                    def data_changelist = params.data_changelist ?: last_good_data

                    if (is_triggered_by_code) {
                        data_changelist = params.data_changelist ?: env.P4_CHANGELIST
                        if (env.frostbite_syncer_setup.toBoolean() == true) {
                            data_changelist = params.data_changelist ?: last_good_code
                        }
                    }

                    def clean_data = params.clean_data

                    if (code_changelist == null || code_changelist == '' || data_changelist == null || data_changelist == '') {
                        echo 'Missing changelist(s), aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]

                    def combine_code_changelist = ''
                    def combine_data_changelist = ''
                    if (branchfile.standard_jobs_settings.combine_bundles?.is_target_branch) {
                        combine_code_changelist = LibJenkins.getLastStableCodeChangelist(env.combine_reference_job)
                        combine_data_changelist = LibJenkins.getLastStableDataChangelist(env.combine_reference_job)
                        inject_map += [
                            'combine_code_changelist': combine_code_changelist,
                            'combine_data_changelist': combine_data_changelist,
                        ]
                    }
                    def jobs = [:]

                    EnvInject(currentBuild, inject_map)

                    // Set display name including SP changelists when available for combined jobs
                    def display_changelists = [data_changelist, code_changelist]
                    if (branchfile.standard_jobs_settings.combine_bundles?.is_target_branch) {
                        display_changelists += [combine_data_changelist, combine_code_changelist]
                    }
                    SetDisplayName(currentBuild, display_changelists)

                    if ((env.main_unverified_branch.toBoolean() == true) || (env.frosty_only_build_on_new_code.toBoolean() == true)) {
                        def last_code_built = LibJenkins.getLastStableCodeChangelist(env.branch_name + '.frosty.start')
                        if ((code_changelist == last_code_built) &amp;&amp; (env.build_without_new_code.toBoolean() == false)) {
                            // Skip build if no code change and disable build_without_new_code flag
                            echo 'Last build was on code CL ' + last_code_built + ' and current code CL is ' + code_changelist + ', aborting build.'
                            currentBuild.result = Result.UNSTABLE.toString()
                            return
                        }

                        echo "Last build was on code CL $last_code_built but current code CL is $code_changelist, proceeding."
                    }

                    def frosty_matrix = branchfile.frosty_matrix

                    // Check if separate combined bundles jobs should be created
                    def use_separate_combined_job = branchfile.standard_jobs_settings.combine_bundles?.use_separate_combined_job ?: false
                    def combined_job_platforms = branchfile.standard_jobs_settings.combine_bundles?.combined_job_platforms ?: []

                    def final_result = Result.SUCCESS
                    def continue_build = true

                    // Create and run combined bundles jobs first if enabled
                    if (use_separate_combined_job &amp;&amp; combined_job_platforms.size() &gt; 0) {
                        echo "Creating separate combined bundles jobs for platforms: ${combined_job_platforms}"

                        def combined_jobs = [:]
                        for (platform in combined_job_platforms) {
                            // Check if this platform has any combine variants in the frosty matrix
                            def has_combine_variants = frosty_matrix.any { matrix_platform -&gt;
                                matrix_platform.name == platform &amp;&amp; matrix_platform.variants?.any { variant -&gt;
                                    variant.format?.contains('combine')
                                }
                            }

                            if (has_combine_variants) {
                                def combined_job_name = env.branch_name + '.combined_bundles.' + platform
                                def combined_job_args = args + [
                                    string(name: 'combine_code_changelist', value: combine_code_changelist),
                                    string(name: 'combine_data_changelist', value: combine_data_changelist),
                                ]

                                combined_jobs[combined_job_name] = {
                                    def downstream_job = build(job: combined_job_name, parameters: combined_job_args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printFailureMessage(this, downstream_job, false)
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }

                        if (combined_jobs.size() &gt; 0) {
                            echo "Running ${combined_jobs.size()} combined bundles jobs in parallel"
                            parallel(combined_jobs)

                            // If any combined bundles job failed, fail the entire scheduler
                            if (final_result != Result.SUCCESS) {
                                echo 'Combined bundles jobs failed, aborting frosty builds'
                                currentBuild.result = final_result.toString()
                                return
                            }
                            echo 'All combined bundles jobs completed successfully'
                        }
                    }
                    for (def run = 0; run &lt;= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit &gt; 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in frosty_matrix) {
                            for (variant in platform.variants) {
                                def job_args = args
                                def rebuild_combine_code_changelist = ''
                                def rebuild_combine_data_changelist = ''
                                if (variant.format.contains('combine')) {
                                    job_args += [
                                        string(name: 'combine_code_changelist', value: combine_code_changelist),
                                        string(name: 'combine_data_changelist', value: combine_data_changelist),
                                    ]
                                    rebuild_combine_code_changelist = combine_code_changelist
                                    rebuild_combine_data_changelist = combine_data_changelist
                                }
                                Boolean allow_failure = variant.allow_failure ?: false
                                def job_name = env.branch_name + '.frosty.' + env.dataset + '.' + platform.name + '.' + variant.format + '.' + variant.region + '.' + variant.config
                                if (NeedsRebuildData(job_name, code_changelist, data_changelist, rebuild_combine_code_changelist, rebuild_combine_data_changelist)) {
                                    if (run &gt; 0 &amp;&amp; IsGameFailure(job_name)) {
                                        if (allow_failure == false) {
                                            final_result = Result.FAILURE
                                            // Set pipeline as failed if there are jobs from IsGameFailure category.
                                            continue_build = false
                                        }
                                        break
                                    } else {
                                        jobs[job_name] = {
                                            def downstream_job = build(job: job_name, parameters: job_args, propagate: false)
                                            if (allow_failure == false) {
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                            }
                                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                            LibJenkins.printRunningJobs(this)
                                        }
                                    }
                                }
                            }
                            if (continue_build == false) {
                                break
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }

                    currentBuild.result = final_result.toString()

                    if (currentBuild.result.toString() == 'SUCCESS') {
                        def frosty_downstream_matrix = branchfile.frosty_downstream_matrix
                        LibCommonCps.triggerDownstreamJobs(
                            this, frosty_downstream_matrix, 'frosty', env.branch_name, branchfile,
                            code_changelist, data_changelist, combine_code_changelist, combine_data_changelist
                        )

                        if (branchfile.code_downstream_matrix?.join(',').contains(':.frosty')) {
                            build(job: env.branch_name + '.bilbo.register-' + env.dataset + '-dronebuild', parameters: args, propagate: false, wait: false)
                        }
                    }

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_frosty
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>