<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Sync trunk-to-dev-na code and deploy a binary build.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>branch_name=trunk-to-dev-na
code_branch=trunk-to-dev-na
code_folder=stage
codepreflight_info_sending=false
data_triggers_patchdata=false
enable_lkg_p4_counters=true
frostbite_syncer_setup=true
main_unverified_branch=false
non_virtual_code_branch=
non_virtual_code_folder=
project_name=bct
retry_limit=1
skip_code_build_if_no_changes=false
smoke_cl_after_success=false
skip_frosty_scheduler=false
slack_notify_bot_code=false
slack_notify_bot_code_nomaster=false
slack_notify_bot_code_stressbulkbuild=false
code_reference_job=
skip_clean_label=false</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>false</string>
                            <string>true</string>
                        </a>
                    </choices>
                    <name>clean_local</name>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.SCMTrigger>
                    <spec>H/5 * * * 1-6
H/5 6-23 * * 7</spec>
                    <ignorePostCommitHooks>false</ignorePostCommitHooks>
                </hudson.triggers.SCMTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.code

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import hudson.model.Result

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)
def masterSettings = GetMasterFile.get_masterfile(BUILD_URL)[0]

/**
 * code_start.groovy
 */
pipeline {
    agent { label '(scheduler &amp;&amp; master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchfile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, ignore_paths, [], settings_map)
                }
            }
        }
        stage('Trigger code jobs') {
            steps {
                script {
                    echo '[Trigger code jobs] Start'
                    def code_changelist = params.code_changelist ?: env.P4_CHANGELIST
                    def skip_code_build_if_no_changes = env.skip_code_build_if_no_changes.toBoolean()
                    def frostbite_syncer_setup = env.frostbite_syncer_setup.toBoolean()
                    def smoke_cl_after_success = env.smoke_cl_after_success.toBoolean()

                    if (env.code_reference_job != '') {
                        echo '[Getting CLs] Start'
                        def reference_code = LibJenkins.getLastStableCodeChangelist(env.code_reference_job)
                        code_changelist = params.code_changelist ?: reference_code
                        echo '[Getting CLs] End'
                    }
                    def clean_local = params.clean_local

                    // If there is no recent clean build, then enable the clean build flag, build clean once a day

                    echo '[RecentCleanBuild] Start Check'
                    if (!env.skip_clean_label) {
                        if (LibCommonCps.isRecentCleanBuildByBuilds(currentBuild, project, branchfile) == false) {
                            clean_local = 'true'
                            echo '[RecentCleanBuild] Run clean local'
                        }
                    }
                    echo '[RecentCleanBuild] End'

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'clean_local', value: clean_local),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]

                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                    boolean build_code_jobs = false
                    if (frostbite_syncer_setup == false || skip_code_build_if_no_changes == false) {
                        build_code_jobs = true
                    } else {
                        def code_check_job = build(job: env.branch_name + '.code.check', parameters: [], propagate: false)
                        if (code_check_job.result.toString() != 'SUCCESS') {
                            echo 'Code check job not successful, aborting.'
                            currentBuild.result = Result.FAILURE.toString()
                            return
                        }
                        echo '[Getting CLs] Start'
                        def last_code_change = LibJenkins.getLastStableCodeChangelist(env.branch_name + '.code.check')
                        def last_code_build = LibJenkins.getLastStableCodeChangelist(env.branch_name + '.code.start')
                        echo '[Getting CLs] End'
                        if (last_code_build == null || last_code_build &lt; last_code_change) {
                            build_code_jobs = true
                        } else {
                            echo 'No new code to build, copying latest code build to filer.'
                            def code_copy_args = [
                                string(name: 'source_changelist', value: last_code_build),
                                string(name: 'current_changelist', value: code_changelist),
                            ]
                            def code_copy_job = build(job: env.branch_name + '.code.copy-to-filer', parameters: code_copy_args, propagate: false)
                            currentBuild.result = code_copy_job.result.toString()
                        }
                    }

                    if (build_code_jobs) {
                        def code_matrix = branchfile.code_matrix

                        def final_result = Result.SUCCESS
                        def continue_build = true
                        for (def run = 0; run &lt;= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit &gt; 0.
                            jobs = [:]
                            final_result = Result.SUCCESS

                            echo '[Building Job List] Start'
                            for (platform in code_matrix) {
                                for (config in platform.configs) {
                                    Boolean allow_failure = false
                                    def config_name = config
                                    def custom_tag = null
                                    if (config instanceof Map) {
                                        allow_failure = config.allow_failure ?: false
                                        config_name = config.name
                                        custom_tag = config?.custom_tag
                                    }

                                    def job_name = env.branch_name + '.code.' + platform.name + '.' + config_name
                                    if (custom_tag != null) {
                                        job_name += ".${custom_tag}"
                                    }

                                    if (NeedsRebuildCode(job_name, code_changelist)) {
                                        if (run &gt; 0 &amp;&amp; IsGameFailure(job_name, allow_failure)) {
                                            if (allow_failure == false) {
                                                final_result = Result.FAILURE
                                                // Set pipeline as failed if there are jobs from IsGameFailure category.
                                                continue_build = false
                                            }
                                            break
                                        } else {
                                            def code_args = args
                                            if (run &gt; 0 &amp;&amp; IsCleanFailure(job_name)) {
                                                code_args = [
                                                    string(name: 'code_changelist', value: code_changelist),
                                                    string(name: 'clean_local', value: 'true'),
                                                ]
                                            }
                                            jobs[job_name] = {
                                                def downstream_job = build(job: job_name, parameters: code_args, propagate: false)
                                                if (allow_failure == false) {
                                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                                }
                                                LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                                LibJenkins.printRunningJobs(this)
                                            }
                                        }
                                    }
                                }
                                if (continue_build == false) {
                                    break
                                }
                            }
                            echo '[Building Job List] End'
                            if (continue_build == false) {
                                break
                            }
                            parallel(jobs)
                            if (final_result == Result.SUCCESS) {
                                break
                            }
                        }
                        currentBuild.result = final_result.toString()
                    }

                    if (currentBuild.result.toString() == 'SUCCESS') {
                        def register_smoke_job = []
                        if (smoke_cl_after_success) {
                            echo '[Register Smoke Code CLs] Start'
                            def code_check_job = build(job: "${env.branch_name}.code.check", parameters: [], propagate: false)
                            if (code_check_job.result.toString() != 'SUCCESS') {
                                echo 'Code check job not successful, cancelling register smoke'
                            } else {
                                def code_change_cl = code_check_job.rawBuild.getEnvironment(TaskListener.NULL)?.code_changelist
                                def last_code_smoked = LibJenkins.getLastStableCodeChangelist("${env.branch_name}.register.smoke")

                                if (code_change_cl == null || last_code_smoked == null || (Integer.parseInt(code_change_cl) &lt;= Integer.parseInt(last_code_smoked)
                                    &amp;&amp; Integer.parseInt(code_changelist) &gt; Integer.parseInt(last_code_smoked))) {
                                    echo "Registering smoke CL for ${code_changelist}"
                                    register_smoke_job = [name: '.register.smoke', args: ['code_changelist']]
                                } else {
                                    echo 'No need to smoke CL'
                                    echo "[Last code CL Smoked]: ${last_code_smoked}"
                                    echo "[Last code CL Changed]: ${code_change_cl}"
                                }
                                echo '[Register Smoke Code CLs] End'
                            }
                        }
                        def code_downstream_matrix = masterSettings.code_downstream_matrix + branchfile.code_downstream_matrix + register_smoke_job
                        for (downstream_job in code_downstream_matrix) {
                            if (downstream_job.trigger_only_on_new_code &amp;&amp; !build_code_jobs) {
                                code_downstream_matrix.remove(downstream_job)
                            }
                        }
                        LibCommonCps.triggerDownstreamJobs(this, code_downstream_matrix, 'code', env.branch_name, branchfile, code_changelist)
                    }

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_code
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    // Set this build as 'cleaned', if necessary
                    SetOptionalCleanBuild(currentBuild, clean_local)

                    DownstreamErrorReporting(currentBuild)
                    echo '[Trigger code jobs] Done'
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, env.slack_notify_bot_code) } }
    }
    post { failure { SlackNotifyBot(currentBuild, env.slack_notify_bot_code) } }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>